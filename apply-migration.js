const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyMigration() {
  try {
    console.log('Applying migration: add_components_to_business_cards');
    
    // Add components column to business_cards table
    const { error: alterError } = await supabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE business_cards 
        ADD COLUMN IF NOT EXISTS components jsonb DEFAULT '[]'::jsonb;
      `
    });

    if (alterError) {
      console.error('Error adding components column:', alterError);
      return;
    }

    // Add index for better performance
    const { error: indexError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE INDEX IF NOT EXISTS idx_business_cards_components 
        ON business_cards USING gin (components);
      `
    });

    if (indexError) {
      console.error('Error creating index:', indexError);
      return;
    }

    console.log('Migration applied successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

applyMigration();
