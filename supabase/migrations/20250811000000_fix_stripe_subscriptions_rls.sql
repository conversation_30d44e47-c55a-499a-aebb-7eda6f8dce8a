/*
  # Fix stripe_subscriptions RLS policies

  1. Problem
    - stripe_subscriptions table has RLS enabled but NO UPDATE policy
    - Only SELECT policy exists, which prevents any updates
    - Super admins cannot activate test subscriptions
    - This breaks the admin dashboard analytics

  2. Solution
    - Add UPDATE policy for super admins to manage all subscriptions
    - Add UPDATE policy for users to update their own subscriptions
    - Keep existing SELECT policy for security

  3. Changes
    - Add "Super admins can update all subscriptions" policy
    - Add "Users can update own subscriptions" policy
    - Ensure proper security while allowing necessary functionality
*/

-- Add UPDATE policy for super admins to manage all subscriptions
CREATE POLICY "Super admins can update all subscriptions"
    ON stripe_subscriptions
    FOR UPDATE
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM user_profiles up 
            WHERE up.user_id = auth.uid() 
            AND up.user_type = 'super_admin'
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM user_profiles up 
            WHERE up.user_id = auth.uid() 
            AND up.user_type = 'super_admin'
        )
    );

-- Add UPDATE policy for users to update their own subscriptions
CREATE POLICY "Users can update own subscriptions"
    ON stripe_subscriptions
    FOR UPDATE
    TO authenticated
    USING (
        customer_id IN (
            SELECT customer_id
            FROM stripe_customers
            WHERE user_id = auth.uid() AND deleted_at IS NULL
        )
        AND deleted_at IS NULL
    )
    WITH CHECK (
        customer_id IN (
            SELECT customer_id
            FROM stripe_customers
            WHERE user_id = auth.uid() AND deleted_at IS NULL
        )
        AND deleted_at IS NULL
    );

-- Add INSERT policy for super admins to create subscriptions
CREATE POLICY "Super admins can insert subscriptions"
    ON stripe_subscriptions
    FOR INSERT
    TO authenticated
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM user_profiles up 
            WHERE up.user_id = auth.uid() 
            AND up.user_type = 'super_admin'
        )
    );

-- Add INSERT policy for users to create their own subscriptions
CREATE POLICY "Users can insert own subscriptions"
    ON stripe_subscriptions
    FOR INSERT
    TO authenticated
    WITH CHECK (
        customer_id IN (
            SELECT customer_id
            FROM stripe_customers
            WHERE user_id = auth.uid() AND deleted_at IS NULL
        )
    );

-- Add DELETE policy for super admins to manage subscriptions
CREATE POLICY "Super admins can delete subscriptions"
    ON stripe_subscriptions
    FOR DELETE
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM user_profiles up 
            WHERE up.user_id = auth.uid() 
            AND up.user_type = 'super_admin'
        )
    );

-- Add DELETE policy for users to delete their own subscriptions
CREATE POLICY "Users can delete own subscriptions"
    ON stripe_subscriptions
    FOR DELETE
    TO authenticated
    USING (
        customer_id IN (
            SELECT customer_id
            FROM stripe_customers
            WHERE user_id = auth.uid() AND deleted_at IS NULL
        )
        AND deleted_at IS NULL
    );
