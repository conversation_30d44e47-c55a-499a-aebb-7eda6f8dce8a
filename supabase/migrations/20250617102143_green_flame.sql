/*
  # Add page background field to business cards

  1. Changes
    - Add `page_background` jsonb column to business_cards table
    - This will store page-wide background settings for premium users

  2. Structure
    - type: 'gradient' | 'image' | 'pattern'
    - value: CSS class or image URL
    - overlay: { enabled: boolean, color: string, opacity: number }
*/

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'business_cards' AND column_name = 'page_background'
  ) THEN
    ALTER TABLE business_cards ADD COLUMN page_background jsonb;
  END IF;
END $$;