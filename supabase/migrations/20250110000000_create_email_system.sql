-- Create email_templates table
CREATE TABLE IF NOT EXISTS email_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    subject VARCHAR(500) NOT NULL,
    html_content TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create email_logs table for tracking sent emails
CREATE TABLE IF NOT EXISTS email_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    template_id UUID REFERENCES email_templates(id),
    recipient_email VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'sent',
    error_message TEXT
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_email_templates_name ON email_templates(name);
CREATE INDEX IF NOT EXISTS idx_email_logs_recipient ON email_logs(recipient_email);
CREATE INDEX IF NOT EXISTS idx_email_logs_sent_at ON email_logs(sent_at);

-- Create trigger function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for email_templates table
CREATE TRIGGER update_email_templates_updated_at 
    BEFORE UPDATE ON email_templates 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE email_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for admin access
CREATE POLICY "Admin can manage email templates" ON email_templates
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Admin can view email logs" ON email_logs
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Admin can insert email logs" ON email_logs
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Insert some sample email templates
INSERT INTO email_templates (name, subject, html_content, description) VALUES
(
    'Welcome Email',
    'Welcome to Our Business Card Platform! 🎉',
    '<h1>Welcome!</h1><p>Thank you for joining our platform!</p>',
    'Default welcome email for new users'
),
(
    'Platform Update',
    '🎉 New Features Available on Our Platform!',
    '<h1>Platform Update!</h1><p>Check out our latest features!</p>',
    'Announcement email for platform updates'
),
(
    'Special Offer',
    '🎁 Special Offer Just for You! Limited Time Only',
    '<h1>Special Offer!</h1><p>Exclusive deal just for you!</p>',
    'Promotional email for special offers'
)
ON CONFLICT (name) DO NOTHING;
