-- Final super admin fix - using only allowed subscription status values
-- and avoiding function return type changes

-- First, let's update the super admin logic to use only allowed values
DO $$
DECLARE
  super_admin_emails text[] := ARRAY['<EMAIL>', '<EMAIL>']; -- Add your super admin emails here
  updated_count integer;
BEGIN
  -- Update super admin users to have permanent access (using 'active' status)
  UPDATE user_profiles 
  SET 
    subscription_end_date = '2099-12-31'::date, -- Far future date (effectively permanent)
    subscription_start_date = CURRENT_DATE,
    subscription_status = 'active', -- Use 'active' instead of 'permanent'
    user_type = 'super_admin' -- Mark them as super admin
  WHERE user_id IN (
    SELECT au.id 
    FROM auth.users au 
    WHERE au.email = ANY(super_admin_emails)
  );
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  RAISE NOTICE 'Updated % users to super admin status', updated_count;
  
  -- Also update any existing users with 'super_admin' type
  UPDATE user_profiles 
  SET 
    subscription_end_date = '2099-12-31'::date,
    subscription_start_date = CURRENT_DATE,
    subscription_status = 'active' -- Use 'active' instead of 'permanent'
  WHERE user_type = 'super_admin';
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  RAISE NOTICE 'Updated % existing super admin users', updated_count;
END $$;

-- Create a function to check if a user is a super admin
CREATE OR REPLACE FUNCTION is_super_admin(user_id_param uuid)
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS(
    SELECT 1 FROM user_profiles 
    WHERE user_id = user_id_param 
      AND user_type = 'super_admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION is_super_admin(uuid) TO authenticated;

-- Update the subscription check functions to exclude super admins (using date filtering instead of status)
-- We'll drop and recreate to avoid return type issues
DROP FUNCTION IF EXISTS get_users_expiring_soon(integer);

CREATE OR REPLACE FUNCTION get_users_expiring_soon(days_ahead integer DEFAULT 3)
RETURNS TABLE(
  user_id uuid,
  email character varying(255),
  user_type text,
  days_until_expiry integer
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    up.user_id,
    au.email,
    up.user_type,
    (up.subscription_end_date - CURRENT_DATE) as days_until_expiry
  FROM user_profiles up
  JOIN auth.users au ON up.user_id = au.id
  WHERE up.subscription_end_date IS NOT NULL
    AND up.subscription_end_date <= CURRENT_DATE + (days_ahead || ' days')::interval
    AND up.user_type != 'super_admin' -- Exclude super admins
    AND up.subscription_end_date < '2099-01-01'::date -- Exclude far future dates (super admins)
  ORDER BY up.subscription_end_date ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the downgrade function to exclude super admins
DROP FUNCTION IF EXISTS check_and_downgrade_subscriptions();

CREATE OR REPLACE FUNCTION check_and_downgrade_subscriptions()
RETURNS text AS $$
DECLARE
  downgraded_count integer := 0;
  user_record record;
BEGIN
  -- Find users with expired subscriptions (excluding super admins)
  FOR user_record IN
    SELECT up.user_id, up.user_type
    FROM user_profiles up
    WHERE up.subscription_end_date IS NOT NULL
      AND up.subscription_end_date < CURRENT_DATE
      AND up.user_type != 'super_admin' -- Exclude super admins
      AND up.subscription_end_date < '2099-01-01'::date -- Exclude far future dates (super admins)
      AND up.subscription_status != 'expired' -- Don't process already expired
  LOOP
    -- Downgrade to free plan
    UPDATE user_profiles 
    SET 
      user_type = 'free',
      subscription_status = 'inactive', -- Use 'inactive' instead of 'expired'
      max_offers = 3,
      has_analytics = false,
      has_ecommerce = false,
      can_change_background = false
    WHERE user_id = user_record.user_id;
    
    -- Log the change
    INSERT INTO user_profiles_history (
      user_id,
      user_type,
      subscription_status,
      max_offers,
      has_analytics,
      has_ecommerce,
      can_change_background,
      change_reason
    ) VALUES (
      user_record.user_id,
      'free',
      'inactive', -- Use 'inactive' instead of 'expired'
      3,
      false,
      false,
      false,
      'Automatic downgrade: ' || user_record.user_type || ' subscription expired'
    );
    
    downgraded_count := downgraded_count + 1;
  END LOOP;
  
  RETURN format('Downgraded %d expired subscriptions (super admins excluded)', downgraded_count);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the get all users function to show super admin status
-- We'll drop and recreate to avoid return type issues
DROP FUNCTION IF EXISTS get_all_users_subscription_status();

CREATE OR REPLACE FUNCTION get_all_users_subscription_status()
RETURNS TABLE(
  user_id uuid,
  email text,
  user_type text,
  subscription_status text,
  subscription_start_date date,
  subscription_end_date date,
  days_until_expiry integer,
  is_expired boolean,
  is_super_admin boolean
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    up.user_id,
    au.email::text,
    up.user_type,
    up.subscription_status,
    up.subscription_start_date::date,
    up.subscription_end_date::date,
    CASE 
      WHEN up.user_type = 'super_admin' THEN NULL -- Super admins don't expire
      WHEN up.subscription_end_date IS NULL THEN NULL
      ELSE (up.subscription_end_date::date - CURRENT_DATE)
    END as days_until_expiry,
    CASE 
      WHEN up.user_type = 'super_admin' THEN false -- Super admins never expire
      WHEN up.subscription_end_date IS NULL THEN false
      ELSE up.subscription_end_date::date <= CURRENT_DATE
    END as is_expired,
    (up.user_type = 'super_admin') as is_super_admin
  FROM user_profiles up
  JOIN auth.users au ON up.user_id = au.id
  ORDER BY 
    up.user_type = 'super_admin' DESC, -- Super admins first
    CASE WHEN up.subscription_end_date IS NULL THEN 1 ELSE 0 END,
    up.subscription_end_date ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users (admins)
GRANT EXECUTE ON FUNCTION get_all_users_subscription_status() TO authenticated;

-- Add comment explaining the super admin system
COMMENT ON FUNCTION is_super_admin(uuid) IS 
'Check if a user is a super admin. Super admins have permanent access and are excluded from subscription expiry checks.';
