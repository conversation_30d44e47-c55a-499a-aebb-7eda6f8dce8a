-- Update old users to have subscription dates for testing the automatic downgrade system
-- This migration sets subscription_end_date to today for users who don't have it set

-- First, let's see what users we're working with
DO $$
DECLARE
  old_user_count integer;
  updated_count integer;
BEGIN
  -- Count users without subscription dates
  SELECT COUNT(*) INTO old_user_count
  FROM user_profiles up
  JOIN auth.users au ON up.user_id = au.id
  WHERE up.subscription_end_date IS NULL 
    AND up.user_type != 'free';
  
  RAISE NOTICE 'Found % users without subscription dates', old_user_count;
  
  -- Update old users to expire today (for testing purposes)
  UPDATE user_profiles 
  SET 
    subscription_end_date = CURRENT_DATE,
    subscription_start_date = CURRENT_DATE - INTERVAL '30 days',
    subscription_status = 'active'
  WHERE subscription_end_date IS NULL 
    AND user_type != 'free';
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  RAISE NOTICE 'Updated % users to expire today', updated_count;
  
  -- Also update users with 'free' type to have proper dates
  UPDATE user_profiles 
  SET 
    subscription_end_date = CURRENT_DATE + INTERVAL '365 days', -- Free users get 1 year
    subscription_start_date = CURRENT_DATE,
    subscription_status = 'active'
  WHERE subscription_end_date IS NULL 
    AND user_type = 'free';
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  RAISE NOTICE 'Updated % free users with proper dates', updated_count;
END $$;

-- Create a function to manually set a user's subscription to expire on a specific date
CREATE OR REPLACE FUNCTION set_user_subscription_expiry(
  target_user_id uuid,
  expiry_date date DEFAULT CURRENT_DATE
)
RETURNS text AS $$
DECLARE
  user_type_val text;
  start_date date;
BEGIN
  -- Get the user's current type
  SELECT up.user_type INTO user_type_val
  FROM user_profiles up
  WHERE up.user_id = target_user_id;
  
  IF NOT FOUND THEN
    RETURN 'User not found';
  END IF;
  
  -- Calculate start date (30 days before expiry for premium, 365 for free)
  IF user_type_val = 'free' THEN
    start_date := expiry_date - INTERVAL '365 days';
  ELSE
    start_date := expiry_date - INTERVAL '30 days';
  END IF;
  
  -- Update the user's subscription dates
  UPDATE user_profiles 
  SET 
    subscription_end_date = expiry_date,
    subscription_start_date = start_date,
    subscription_status = CASE 
      WHEN expiry_date <= CURRENT_DATE THEN 'expired'
      ELSE 'active'
    END
  WHERE user_id = target_user_id;
  
  RETURN format('User %s subscription set to expire on %s (type: %s)', 
                target_user_id, expiry_date, user_type_val);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users (admins)
GRANT EXECUTE ON FUNCTION set_user_subscription_expiry(uuid, date) TO authenticated;

-- Create a function to get all users with their subscription status
CREATE OR REPLACE FUNCTION get_all_users_subscription_status()
RETURNS TABLE(
  user_id uuid,
  email text,
  user_type text,
  subscription_status text,
  subscription_start_date date,
  subscription_end_date date,
  days_until_expiry integer,
  is_expired boolean
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    up.user_id,
    au.email::text,
    up.user_type,
    up.subscription_status,
    up.subscription_start_date,
    up.subscription_end_date,
    CASE 
      WHEN up.subscription_end_date IS NULL THEN NULL
      ELSE (up.subscription_end_date - CURRENT_DATE)
    END as days_until_expiry,
    CASE 
      WHEN up.subscription_end_date IS NULL THEN false
      ELSE up.subscription_end_date <= CURRENT_DATE
    END as is_expired
  FROM user_profiles up
  JOIN auth.users au ON up.user_id = au.id
  ORDER BY 
    CASE WHEN up.subscription_end_date IS NULL THEN 1 ELSE 0 END,
    up.subscription_end_date ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users (admins)
GRANT EXECUTE ON FUNCTION get_all_users_subscription_status() TO authenticated;
