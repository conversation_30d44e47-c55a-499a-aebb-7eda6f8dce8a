/*
  # Fix Date Calculation in Expiring Users Function

  This migration fixes the type mismatch in the get_users_expiring_soon function.
  
  Problem: Function returns interval instead of integer for days_until_expiry.
  Solution: Fix the date calculation to return proper integer values.
*/

-- Drop and recreate the function with proper date handling
DROP FUNCTION IF EXISTS get_users_expiring_soon(integer);

CREATE OR REPLACE FUNCTION get_users_expiring_soon(days_ahead integer DEFAULT 30)
RETURNS TABLE(
  user_id uuid,
  email character varying(255),
  user_type text,
  days_until_expiry integer
) AS $$
BEGIN
  RAISE NOTICE 'get_users_expiring_soon called with days_ahead: %', days_ahead;
  RAISE NOTICE 'Current date: %', CURRENT_DATE;
  
  RETURN QUERY
  SELECT 
    up.user_id,
    au.email,
    up.user_type,
    EXTRACT(DAY FROM (up.subscription_end_date - CURRENT_DATE))::integer as days_until_expiry
  FROM user_profiles up
  JOIN auth.users au ON up.user_id = au.id
  WHERE up.subscription_end_date IS NOT NULL
    AND up.subscription_end_date <= CURRENT_DATE + (days_ahead || ' days')::interval
    AND up.user_type NOT IN ('super_admin', 'lifetime') -- Exclude permanent users
    AND up.subscription_end_date < '2099-01-01'::date -- Exclude far future dates
    AND up.subscription_status != 'inactive' -- Only active subscriptions
  ORDER BY up.subscription_end_date ASC;
  
  RAISE NOTICE 'Function completed successfully';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_users_expiring_soon(integer) TO authenticated;

-- Test the function
DO $$
DECLARE
  result_count integer;
  test_user record;
BEGIN
  SELECT COUNT(*) INTO result_count FROM get_users_expiring_soon(30);
  RAISE NOTICE 'get_users_expiring_soon(30) returned % users', result_count;
  
  -- Show the first few results
  FOR test_user IN SELECT * FROM get_users_expiring_soon(30) LIMIT 3
  LOOP
    RAISE NOTICE 'Test user: %, Email: %, Type: %, Days Left: %', 
      test_user.user_id, test_user.email, test_user.user_type, test_user.days_until_expiry;
  END LOOP;
END $$;
