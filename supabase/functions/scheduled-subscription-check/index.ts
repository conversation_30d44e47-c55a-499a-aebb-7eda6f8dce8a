import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Create Supabase client with service role key
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    console.log('🔄 Starting scheduled subscription check...');

    // Step 1: Check and downgrade expired subscriptions
    console.log('📉 Checking for expired subscriptions...');
    const { data: downgradeResult, error: downgradeError } = await supabase
      .rpc('check_and_downgrade_subscriptions');
    
    if (downgradeError) {
      console.error('❌ Error checking expired subscriptions:', downgradeError);
      throw downgradeError;
    }

    console.log('✅ Downgrade check completed:', downgradeResult);

    // Step 2: Get users expiring in the next 3 days for notifications
    console.log('📧 Checking for users needing expiry notifications...');
    const { data: expiringUsers, error: expiringError } = await supabase
      .rpc('get_users_expiring_soon', { days_ahead: 3 });
    
    if (expiringError) {
      console.error('❌ Error getting expiring users:', expiringError);
      throw expiringError;
    }

    console.log(`📊 Found ${expiringUsers?.length || 0} users expiring soon`);

    // Step 3: Send expiry notifications to users expiring in next 3 days
    let notificationsSent = 0;
    let notificationsFailed = 0;

    if (expiringUsers && expiringUsers.length > 0) {
      console.log('📨 Sending expiry notifications...');
      
      for (const user of expiringUsers) {
        try {
          // Create the email content
          const emailHtml = `
            <!DOCTYPE html>
            <html>
            <head>
              <meta charset="utf-8">
              <title>Your Subscription is Expiring Soon</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
              <div style="background: linear-gradient(135deg, #0B1B2A 0%, #1a2a3a 100%); padding: 30px; border-radius: 10px; margin-bottom: 20px;">
                <h1 style="color: #F5B700; margin: 0; text-align: center; font-size: 28px;">⚠️ Subscription Expiring Soon</h1>
              </div>
              
              <div style="background: #fff; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <h2 style="color: #0B1B2A; margin-top: 0;">Hello!</h2>
                
                <p>Your <strong>${user.user_type.replace('_', ' ').toUpperCase()}</strong> subscription will expire in <strong style="color: #F5B700;">${user.days_until_expiry} days</strong>.</p>
                
                <p>To continue enjoying premium features like:</p>
                <ul style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #F5B700;">
                  <li>✨ Unlimited business cards</li>
                  <li>📊 Advanced analytics</li>
                  <li>🛒 E-commerce integration</li>
                  <li>🎨 Custom backgrounds</li>
                </ul>
                
                <p>Please renew your subscription before it expires.</p>
                
                <div style="text-align: center; margin: 30px 0;">
                  <a href="${supabaseUrl.replace('/rest/v1', '')}/renew" 
                     style="background: #F5B700; color: #0B1B2A; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">
                    Renew Subscription
                  </a>
                </div>
                
                <p style="color: #666; font-size: 14px;">
                  If you have any questions, please contact our support team.
                </p>
              </div>
              
              <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
                <p>© 2025 Buzzz. All rights reserved.</p>
              </div>
            </body>
            </html>
          `;

          // Send the email using the send-email function
          const emailResponse = await fetch(`${supabaseUrl}/functions/v1/send-email`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${supabaseServiceKey}`
            },
            body: JSON.stringify({
              to: user.email,
              subject: `⚠️ Your ${user.user_type.replace('_', ' ').toUpperCase()} subscription expires in ${user.days_until_expiry} days`,
              html: emailHtml
            })
          });

          if (emailResponse.ok) {
            console.log(`✅ Email sent successfully to ${user.email}`);
            notificationsSent++;
          } else {
            console.error(`❌ Failed to send email to ${user.email}:`, await emailResponse.text());
            notificationsFailed++;
          }
        } catch (error) {
          console.error(`❌ Error sending notification to ${user.email}:`, error);
          notificationsFailed++;
        }
      }
    }

    // Step 4: Log the results
    const summary = {
      timestamp: new Date().toISOString(),
      downgrade_result: downgradeResult,
      expiring_users_count: expiringUsers?.length || 0,
      notifications_sent: notificationsSent,
      notifications_failed: notificationsFailed,
      status: 'completed'
    };

    console.log('📋 Scheduled check summary:', summary);

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Scheduled subscription check completed successfully',
        summary: summary
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('❌ Scheduled subscription check failed:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Scheduled subscription check failed', 
        details: error.message,
        timestamp: new Date().toISOString()
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
