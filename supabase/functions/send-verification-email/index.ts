import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { email, verification_link, user_name } = await req.json()

    if (!email || !verification_link) {
      throw new Error('Missing required fields: email and verification_link')
    }

    // Initialize Resend
    const resend = new (await import('npm:resend')).Resend(Deno.env.get('RESEND_API_KEY'))

    // Beautiful email verification template with your brand colors
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff;">
        <div style="background: #0B1B2A; padding: 30px; text-align: center;">
          <h1 style="color: #F5B700; margin: 0; font-size: 32px; font-weight: bold;">Verify Your Email</h1>
          <p style="color: #ffffff; margin: 10px 0 0 0; font-size: 18px;">Buzzz - Digital Business Cards</p>
        </div>
        
        <div style="padding: 40px 30px;">
          <h2 style="color: #0B1B2A; margin: 0 0 20px 0; font-size: 24px;">Almost there! 🎯</h2>
          <p style="color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
            ${user_name ? `Hello ${user_name}! ` : ''}Thanks for signing up for Buzzz! To complete your registration and start creating amazing digital business cards, please verify your email address.
          </p>
          
          <div style="background: #f8f9fa; border-left: 4px solid #F5B700; padding: 20px; margin: 30px 0;">
            <h3 style="color: #0B1B2A; margin: 0 0 15px 0; font-size: 20px;">🔐 Why Verify?</h3>
            <ul style="color: #333333; font-size: 16px; line-height: 1.6; margin: 0; padding-left: 20px;">
              <li>Secure your account</li>
              <li>Access all features</li>
              <li>Receive important updates</li>
              <li>Connect with other professionals</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin: 40px 0;">
            <a href="${verification_link}" style="background: #F5B700; color: #0B1B2A; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px; display: inline-block;">Verify Email Address</a>
          </div>
          
          <p style="color: #666666; font-size: 14px; line-height: 1.6; margin: 30px 0 0 0;">
            If the button doesn't work, copy and paste this link into your browser:<br>
            <span style="color: #F5B700; word-break: break-all;">${verification_link}</span>
          </p>
          
          <p style="color: #666666; font-size: 14px; line-height: 1.6; margin: 20px 0 0 0;">
            This link will expire in 24 hours. If you didn't create an account with Buzzz, you can safely ignore this email.
          </p>
        </div>
        
        <div style="background: #f8f9fa; padding: 30px; text-align: center;">
          <p style="color: #666666; font-size: 14px; margin: 0;">
            © 2024 Buzzz. All rights reserved.
          </p>
        </div>
      </div>
    `

    // Send email via Resend
    const { data, error } = await resend.emails.send({
      from: '<EMAIL>',
      to: email,
      subject: 'Verify Your Email - Buzzz',
      html: htmlContent,
    })

    if (error) {
      console.error('Resend API error:', error)
      throw new Error(`Failed to send email: ${error.message}`)
    }

    console.log('Verification email sent successfully:', data)

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Verification email sent successfully',
        data 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    console.error('Error in send-verification-email function:', error)
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Unknown error occurred' 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400 
      }
    )
  }
})
