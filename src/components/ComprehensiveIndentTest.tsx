import React, { useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

export default function ComprehensiveIndentTest() {
  const [content, setContent] = useState(`
    <p>Normal paragraph - select this and try indent buttons</p>
    <p>Another paragraph to test</p>
    <ul>
      <li>Normal bullet point</li>
      <li>Another bullet to test</li>
    </ul>
    <ol>
      <li>Normal numbered item</li>
      <li>Another number to test</li>
    </ol>
  `);

  // Test different toolbar configurations
  const [configType, setConfigType] = useState('standard');

  const getQuillConfig = () => {
    switch (configType) {
      case 'minimal':
        return {
          modules: {
            toolbar: [
              [{ 'indent': '-1'}, { 'indent': '+1' }]
            ]
          },
          formats: ['indent']
        };
      
      case 'standard':
        return {
          modules: {
            toolbar: [
              ['bold', 'italic'],
              [{ 'list': 'ordered'}, { 'list': 'bullet' }],
              [{ 'indent': '-1'}, { 'indent': '+1' }],
              ['clean']
            ]
          },
          formats: ['bold', 'italic', 'list', 'bullet', 'ordered', 'indent']
        };
      
      case 'full':
        return {
          modules: {
            toolbar: [
              [{ 'header': [1, 2, 3, false] }],
              ['bold', 'italic', 'underline', 'strike'],
              [{ 'color': [] }, { 'background': [] }],
              [{ 'list': 'ordered'}, { 'list': 'bullet' }],
              [{ 'indent': '-1'}, { 'indent': '+1' }],
              [{ 'align': [] }],
              ['link', 'image'],
              ['blockquote', 'code-block'],
              ['clean']
            ],
            clipboard: {
              matchVisual: false,
            }
          },
          formats: [
            'header', 'bold', 'italic', 'underline', 'strike',
            'color', 'background', 'list', 'bullet', 'ordered',
            'align', 'link', 'image', 'blockquote', 'code-block', 'indent'
          ]
        };
      
      default:
        return {
          modules: { toolbar: [] },
          formats: []
        };
    }
  };

  const config = getQuillConfig();

  const loadPreIndentedContent = () => {
    setContent(`
      <h2>Pre-indented Content Test</h2>
      <p>Normal paragraph</p>
      <p class="ql-indent-1">This should be indented level 1</p>
      <p class="ql-indent-2">This should be indented level 2</p>
      <p class="ql-indent-3">This should be indented level 3</p>
      <ul>
        <li>Normal bullet</li>
        <li class="ql-indent-1">Indented bullet level 1</li>
        <li class="ql-indent-2">Indented bullet level 2</li>
      </ul>
      <ol>
        <li>Normal number</li>
        <li class="ql-indent-1">Indented number level 1</li>
        <li class="ql-indent-2">Indented number level 2</li>
      </ol>
    `);
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Comprehensive Indent Test</h1>
        <p className="text-gray-600 mb-4">
          Test indent functionality with different toolbar configurations.
        </p>
        
        {/* Configuration Selector */}
        <div className="flex gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Toolbar Configuration:
            </label>
            <select
              value={configType}
              onChange={(e) => setConfigType(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="minimal">Minimal (Indent only)</option>
              <option value="standard">Standard (Your current config)</option>
              <option value="full">Full (All features)</option>
            </select>
          </div>
          <div className="flex items-end">
            <button
              onClick={loadPreIndentedContent}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Load Pre-indented Content
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Editor */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-2">
            Editor ({configType} config)
          </h2>
          <div className="border border-gray-300 rounded-lg overflow-hidden">
            <ReactQuill
              key={configType} // Force re-render when config changes
              theme="snow"
              value={content}
              onChange={setContent}
              modules={config.modules}
              formats={config.formats}
              placeholder="Type here and test indentation..."
              style={{ minHeight: '400px' }}
            />
          </div>
          
          {/* Toolbar Analysis */}
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-800 mb-2">Toolbar Analysis:</h4>
            <div className="text-sm text-gray-600">
              <p><strong>Config:</strong> {configType}</p>
              <p><strong>Indent in toolbar:</strong> {config.modules.toolbar.some((group: any) => 
                Array.isArray(group) && group.some((item: any) => 
                  typeof item === 'object' && item.indent
                )
              ) ? '✅ Yes' : '❌ No'}</p>
              <p><strong>Indent in formats:</strong> {config.formats.includes('indent') ? '✅ Yes' : '❌ No'}</p>
            </div>
          </div>
        </div>

        {/* Preview */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-2">Landing Page Preview</h2>
          <div className="border border-gray-300 rounded-lg p-6 bg-white">
            <div 
              className="prose prose-sm max-w-none landing-content"
              dangerouslySetInnerHTML={{ __html: content }}
            />
          </div>
        </div>
      </div>

      {/* HTML Analysis */}
      <div className="mt-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">HTML Analysis</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Raw HTML Output:</h4>
            <pre className="bg-gray-100 p-4 rounded-lg text-xs overflow-auto max-h-60">
              {content}
            </pre>
          </div>
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Indent Class Detection:</h4>
            <div className="bg-gray-100 p-4 rounded-lg text-sm">
              {['ql-indent-1', 'ql-indent-2', 'ql-indent-3', 'ql-indent-4'].map(className => (
                <div key={className} className="flex justify-between">
                  <span>{className}:</span>
                  <span className={content.includes(className) ? 'text-green-600' : 'text-red-600'}>
                    {content.includes(className) ? '✅ Found' : '❌ Not found'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-6 bg-blue-50 p-4 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">Testing Instructions:</h3>
        <ol className="text-blue-700 space-y-1 text-sm list-decimal list-inside">
          <li>Try different toolbar configurations using the dropdown</li>
          <li>Look for → and ← buttons in the editor toolbar</li>
          <li>Select text in the editor and click the indent buttons</li>
          <li>Check if ql-indent-X classes appear in the HTML analysis</li>
          <li>Check if indentation appears in the preview</li>
          <li>Try the "Load Pre-indented Content" to see if CSS works</li>
        </ol>
      </div>

      {/* Troubleshooting */}
      <div className="mt-4 bg-yellow-50 p-4 rounded-lg">
        <h3 className="font-semibold text-yellow-800 mb-2">Troubleshooting:</h3>
        <ul className="text-yellow-700 space-y-1 text-sm">
          <li>• If buttons are missing: Check toolbar configuration</li>
          <li>• If buttons don't work: Check formats array includes 'indent'</li>
          <li>• If HTML doesn't change: ReactQuill might not be applying classes</li>
          <li>• If preview doesn't show indentation: Check CSS rules</li>
        </ul>
      </div>

      {/* Results Summary */}
      <div className="mt-4 bg-green-50 p-4 rounded-lg">
        <h3 className="font-semibold text-green-800 mb-2">What Should Work:</h3>
        <ul className="text-green-700 space-y-1 text-sm">
          <li>✅ Indent buttons visible in toolbar (→ and ←)</li>
          <li>✅ Clicking buttons adds/removes ql-indent-X classes</li>
          <li>✅ Pre-indented content displays with proper spacing</li>
          <li>✅ Both paragraphs and list items can be indented</li>
        </ul>
      </div>
    </div>
  );
}
