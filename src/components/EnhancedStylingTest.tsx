import React, { useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

export default function EnhancedStylingTest() {
  const [content, setContent] = useState(`
    <h1>Main Heading (H1)</h1>
    <p>This is a paragraph with proper spacing. It demonstrates the enhanced typography and spacing that has been applied to all elements.</p>
    
    <h2>Secondary Heading (H2)</h2>
    <p>Another paragraph to show consistent spacing between different elements. Notice the proper margins and padding.</p>
    
    <h3>Tertiary Heading (H3)</h3>
    <p>Here's a paragraph with <strong>bold text</strong>, <em>italic text</em>, and a <a href="https://example.com">link</a> to demonstrate inline styling.</p>
    
    <h4>Quaternary Heading (H4)</h4>
    <p>This paragraph contains <code>inline code</code> and shows how different text elements are styled.</p>
    
    <ul>
      <li>First bullet point with proper spacing</li>
      <li>Second bullet point</li>
      <li class="ql-indent-1">Indented bullet point level 1</li>
      <li class="ql-indent-2">Indented bullet point level 2</li>
      <li>Back to normal bullet</li>
    </ul>
    
    <ol>
      <li>First numbered item</li>
      <li>Second numbered item</li>
      <li class="ql-indent-1">Indented numbered item level 1</li>
      <li class="ql-indent-2">Indented numbered item level 2</li>
      <li>Back to normal numbering</li>
    </ol>
    
    <blockquote>
      This is a blockquote with enhanced styling. It has proper padding, background, and border styling to make it stand out from regular content.
    </blockquote>
    
    <p>Here's a paragraph with various formatting:</p>
    <p class="ql-indent-1">This paragraph is indented level 1</p>
    <p class="ql-indent-2">This paragraph is indented level 2</p>
    <p class="ql-indent-3">This paragraph is indented level 3</p>
    
    <pre>
    This is a code block
    with multiple lines
    and proper formatting
    </pre>
    
    <p>Final paragraph to show the overall flow and spacing of the content.</p>
  `);

  // Standard configuration
  const quillModules = {
    toolbar: [
      [{ 'header': [1, 2, 3, 4, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      [{ 'align': [] }],
      ['link', 'image'],
      ['blockquote', 'code-block'],
      ['clean']
    ],
    clipboard: {
      matchVisual: false,
    }
  };

  const quillFormats = [
    'header', 'bold', 'italic', 'underline', 'strike',
    'color', 'background', 'list', 'bullet', 'ordered',
    'align', 'link', 'image', 'blockquote', 'code-block', 'indent'
  ];

  const loadSampleContent = () => {
    setContent(`
      <h1>Enhanced Typography Demo</h1>
      <p>This content showcases all the enhanced styling features with proper spacing and typography.</p>
      
      <h2>Features Demonstrated</h2>
      <ul>
        <li>Proper heading spacing (H1-H4)</li>
        <li>Enhanced paragraph spacing</li>
        <li>Better list formatting</li>
        <li>Improved indentation</li>
        <li>Styled blockquotes</li>
        <li>Enhanced links and code</li>
      </ul>
      
      <h3>Typography Examples</h3>
      <p>This paragraph contains <strong>bold text</strong>, <em>italic text</em>, <u>underlined text</u>, and <s>strikethrough text</s>.</p>
      
      <h4>Code Examples</h4>
      <p>Here's some <code>inline code</code> and below is a code block:</p>
      
      <pre>function example() {
  return "This is a code block";
}</pre>
      
      <blockquote>
        "This is an enhanced blockquote with beautiful styling and proper spacing."
      </blockquote>
      
      <h3>Indentation Examples</h3>
      <p>Normal paragraph</p>
      <p class="ql-indent-1">Indented paragraph level 1</p>
      <p class="ql-indent-2">Indented paragraph level 2</p>
      <p class="ql-indent-3">Indented paragraph level 3</p>
      
      <h3>List Examples</h3>
      <ul>
        <li>Normal bullet</li>
        <li class="ql-indent-1">Indented bullet level 1</li>
        <li class="ql-indent-2">Indented bullet level 2</li>
        <li>Back to normal</li>
      </ul>
      
      <ol>
        <li>Normal number</li>
        <li class="ql-indent-1">Indented number level 1</li>
        <li class="ql-indent-2">Indented number level 2</li>
        <li>Back to normal</li>
      </ol>
      
      <p>Visit our <a href="https://example.com">website</a> for more information!</p>
    `);
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Enhanced Styling Demo</h1>
        <p className="text-gray-600 mb-4">
          Test the enhanced typography and spacing for all elements in both the editor and landing page preview.
        </p>
        <div className="flex gap-4 mb-4">
          <button
            onClick={loadSampleContent}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Load Sample Content
          </button>
          <button
            onClick={() => setContent('')}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Clear
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Editor */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Editor</h2>
          <p className="text-sm text-gray-600 mb-4">
            Create and edit content with enhanced styling.
          </p>
          <div className="border border-gray-300 rounded-lg overflow-hidden">
            <ReactQuill
              theme="snow"
              value={content}
              onChange={setContent}
              modules={quillModules}
              formats={quillFormats}
              placeholder="Start typing to see the enhanced styling..."
              style={{ minHeight: '500px' }}
            />
          </div>
        </div>

        {/* Landing Page Preview */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Landing Page Preview</h2>
          <p className="text-sm text-gray-600 mb-4">
            How your content will look on the landing page with enhanced styling.
          </p>
          <div className="border border-gray-300 rounded-lg p-6 bg-white max-h-[600px] overflow-y-auto">
            <div 
              className="prose prose-sm max-w-none landing-content"
              dangerouslySetInnerHTML={{ __html: content }}
            />
          </div>
        </div>
      </div>

      {/* Styling Features */}
      <div className="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="font-semibold text-blue-800 mb-2">Typography</h3>
          <ul className="text-blue-700 text-sm space-y-1">
            <li>• Enhanced heading spacing (H1-H4)</li>
            <li>• Proper paragraph margins</li>
            <li>• Consistent line heights</li>
            <li>• Better font weights</li>
          </ul>
        </div>

        <div className="bg-green-50 p-4 rounded-lg">
          <h3 className="font-semibold text-green-800 mb-2">Lists & Indentation</h3>
          <ul className="text-green-700 text-sm space-y-1">
            <li>• Proper bullet/number spacing</li>
            <li>• Enhanced indentation levels</li>
            <li>• Better list item margins</li>
            <li>• Consistent nested list styling</li>
          </ul>
        </div>

        <div className="bg-purple-50 p-4 rounded-lg">
          <h3 className="font-semibold text-purple-800 mb-2">Special Elements</h3>
          <ul className="text-purple-700 text-sm space-y-1">
            <li>• Styled blockquotes</li>
            <li>• Enhanced code blocks</li>
            <li>• Better link styling</li>
            <li>• Improved inline code</li>
          </ul>
        </div>
      </div>

      {/* Style Guide */}
      <div className="mt-8 bg-gray-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Style Guide</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-800 mb-2">Spacing Values:</h4>
            <ul className="text-gray-600 text-sm space-y-1">
              <li>• H1: 2rem top, 1rem bottom</li>
              <li>• H2: 1.75rem top, 0.875rem bottom</li>
              <li>• H3: 1.5rem top, 0.75rem bottom</li>
              <li>• H4: 1.25rem top, 0.625rem bottom</li>
              <li>• Paragraphs: 1rem bottom</li>
              <li>• Lists: 1rem top/bottom</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-800 mb-2">Indentation Levels:</h4>
            <ul className="text-gray-600 text-sm space-y-1">
              <li>• Level 1: 3rem left margin</li>
              <li>• Level 2: 4.5rem left margin</li>
              <li>• Level 3: 6rem left margin</li>
              <li>• Level 4+: 1.5rem increments</li>
              <li>• All levels: 0.75rem top/bottom</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
