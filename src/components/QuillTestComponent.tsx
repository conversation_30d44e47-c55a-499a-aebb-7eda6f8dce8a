import React, { useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

export default function QuillTestComponent() {
  const [content, setContent] = useState(`
    <h2>Test Content</h2>
    <p>This is a test to verify bullet points and indentation work properly.</p>
    <ul>
      <li>First bullet point</li>
      <li>Second bullet point</li>
      <li>Third bullet point with some longer text to see how it wraps</li>
    </ul>
    <ol>
      <li>First numbered item</li>
      <li>Second numbered item</li>
      <li>Third numbered item</li>
    </ol>
    <p>Try creating new lists and using the indent buttons!</p>
  `);

  // Enhanced Quill configuration
  const quillModules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      [{ 'align': [] }],
      ['link', 'image'],
      ['blockquote', 'code-block'],
      ['clean']
    ],
    clipboard: {
      matchVisual: false,
    },
    keyboard: {
      bindings: {
        tab: {
          key: 9,
          handler: function(range: any, context: any) {
            this.quill.history.cutoff();
            const delta = new (this.quill.constructor.import('delta'))()
              .retain(range.index)
              .delete(range.length)
              .insert('\t');
            this.quill.updateContents(delta, 'user');
            this.quill.history.cutoff();
            this.quill.setSelection(range.index + 1, 'silent');
          }
        }
      }
    }
  };

  const quillFormats = [
    'header', 'bold', 'italic', 'underline', 'strike',
    'color', 'background', 'list', 'bullet', 'ordered',
    'align', 'link', 'image', 'blockquote', 'code-block', 'indent'
  ];

  const testBullets = () => {
    setContent(`
      <h3>Bullet Point Test</h3>
      <ul>
        <li>• First bullet (should show bullet)</li>
        <li>• Second bullet (should show bullet)</li>
        <li>• Third bullet (should show bullet)</li>
      </ul>
      <ol>
        <li>1. First number (should show number)</li>
        <li>2. Second number (should show number)</li>
        <li>3. Third number (should show number)</li>
      </ol>
    `);
  };

  const testIndentation = () => {
    setContent(`
      <h3>Indentation Test</h3>
      <ul>
        <li>Level 1 bullet</li>
        <li class="ql-indent-1">Level 2 bullet (indented)</li>
        <li class="ql-indent-2">Level 3 bullet (more indented)</li>
        <li>Back to level 1</li>
      </ul>
      <p>Normal paragraph</p>
      <p class="ql-indent-1">Indented paragraph</p>
      <p class="ql-indent-2">More indented paragraph</p>
    `);
  };

  const clearContent = () => {
    setContent('');
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">ReactQuill Bullet & Indent Test</h1>
        <div className="flex gap-4 mb-4">
          <button
            onClick={testBullets}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Test Bullets
          </button>
          <button
            onClick={testIndentation}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            Test Indentation
          </button>
          <button
            onClick={clearContent}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Clear
          </button>
        </div>
      </div>

      <div className="space-y-6">
        {/* Editor */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-2">Editor</h2>
          <div className="border border-gray-300 rounded-lg overflow-hidden">
            <ReactQuill
              theme="snow"
              value={content}
              onChange={setContent}
              modules={quillModules}
              formats={quillFormats}
              placeholder="Type here to test bullets and indentation..."
              style={{ minHeight: '300px' }}
            />
          </div>
        </div>

        {/* Preview */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-2">Preview (How it will look)</h2>
          <div 
            className="border border-gray-300 rounded-lg p-4 bg-white prose prose-sm max-w-none"
            dangerouslySetInnerHTML={{ __html: content }}
          />
        </div>

        {/* Raw HTML */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-2">Raw HTML Output</h2>
          <pre className="bg-gray-100 p-4 rounded-lg text-xs overflow-auto max-h-60">
            {content || 'No content'}
          </pre>
        </div>

        {/* Instructions */}
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h3 className="font-semibold text-yellow-800 mb-2">Testing Instructions:</h3>
          <ul className="text-yellow-700 space-y-1 text-sm">
            <li>• Click the bullet list button in the toolbar and type some items</li>
            <li>• Click the numbered list button and type some items</li>
            <li>• Select text and use the indent buttons (→ and ←) to test indentation</li>
            <li>• Try using Tab key for indentation</li>
            <li>• Check if bullets and numbers appear properly in the preview</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
