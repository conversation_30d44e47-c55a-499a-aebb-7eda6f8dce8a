import React, { useState, useRef, useEffect } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

export default function QuillIndentDebug() {
  const [content, setContent] = useState(`
    <p>Normal paragraph</p>
    <p>Select this paragraph and try to indent it</p>
    <ul>
      <li>Normal bullet</li>
      <li>Select this bullet and try to indent it</li>
    </ul>
  `);

  const quillRef = useRef<ReactQuill>(null);

  // Simple configuration focusing on indent
  const quillModules = {
    toolbar: [
      ['bold', 'italic'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }], // Focus on these buttons
      ['clean']
    ]
  };

  const quillFormats = [
    'bold', 'italic', 'list', 'bullet', 'ordered', 'indent'
  ];

  const manualIndent = () => {
    if (quillRef.current) {
      const quill = quillRef.current.getEditor();
      const range = quill.getSelection();
      if (range) {
        quill.format('indent', '+1');
      }
    }
  };

  const manualOutdent = () => {
    if (quillRef.current) {
      const quill = quillRef.current.getEditor();
      const range = quill.getSelection();
      if (range) {
        quill.format('indent', '-1');
      }
    }
  };

  const addTestContent = () => {
    setContent(`
      <p>Test paragraph 1</p>
      <p class="ql-indent-1">Test paragraph 2 (should be indented)</p>
      <p class="ql-indent-2">Test paragraph 3 (should be more indented)</p>
      <ul>
        <li>Bullet 1</li>
        <li class="ql-indent-1">Bullet 2 (should be indented)</li>
        <li class="ql-indent-2">Bullet 3 (should be more indented)</li>
      </ul>
    `);
  };

  useEffect(() => {
    // Debug: Check if Quill is loaded and indent module is available
    if (quillRef.current) {
      const quill = quillRef.current.getEditor();
      console.log('Quill instance:', quill);
      console.log('Available formats:', quill.getFormat());
      
      // Check if indent is in the toolbar
      const toolbar = quill.getModule('toolbar');
      console.log('Toolbar module:', toolbar);
    }
  }, []);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Quill Indent Debug</h1>
        <p className="text-gray-600 mb-4">
          Debug the indent functionality step by step.
        </p>
        <div className="flex gap-4 mb-4">
          <button
            onClick={addTestContent}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Add Test Content
          </button>
          <button
            onClick={manualIndent}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            Manual Indent →
          </button>
          <button
            onClick={manualOutdent}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Manual Outdent ←
          </button>
        </div>
      </div>

      {/* Editor */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">Editor</h2>
        <div className="border border-gray-300 rounded-lg overflow-hidden">
          <ReactQuill
            ref={quillRef}
            theme="snow"
            value={content}
            onChange={setContent}
            modules={quillModules}
            formats={quillFormats}
            placeholder="Type here and test indentation..."
            style={{ minHeight: '300px' }}
          />
        </div>
      </div>

      {/* Preview */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">Preview</h2>
        <div className="border border-gray-300 rounded-lg p-4 bg-white">
          <div 
            className="prose prose-sm max-w-none landing-content"
            dangerouslySetInnerHTML={{ __html: content }}
          />
        </div>
      </div>

      {/* Raw HTML */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">Raw HTML</h2>
        <pre className="bg-gray-100 p-4 rounded-lg text-xs overflow-auto max-h-40">
          {content}
        </pre>
      </div>

      {/* Debug Info */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold text-gray-800 mb-2">Debug Checklist:</h3>
        <ul className="text-gray-700 space-y-1 text-sm">
          <li>1. Check if you can see → and ← buttons in the toolbar</li>
          <li>2. Select some text and click the indent buttons</li>
          <li>3. Try the manual indent/outdent buttons above</li>
          <li>4. Check if ql-indent-X classes appear in the HTML</li>
          <li>5. Check if indentation appears in the preview</li>
          <li>6. Open browser console to see debug logs</li>
        </ul>
      </div>

      {/* Toolbar Button Check */}
      <div className="mt-4 bg-blue-50 p-4 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">Toolbar Button Check:</h3>
        <p className="text-blue-700 text-sm mb-2">
          Look for these buttons in the editor toolbar:
        </p>
        <div className="flex gap-2">
          <div className="bg-white border border-gray-300 px-3 py-1 rounded text-sm">
            ← (Outdent)
          </div>
          <div className="bg-white border border-gray-300 px-3 py-1 rounded text-sm">
            → (Indent)
          </div>
        </div>
        <p className="text-blue-700 text-xs mt-2">
          If you don't see these buttons, there might be a configuration issue.
        </p>
      </div>

      {/* Expected Behavior */}
      <div className="mt-4 bg-green-50 p-4 rounded-lg">
        <h3 className="font-semibold text-green-800 mb-2">Expected Behavior:</h3>
        <ul className="text-green-700 space-y-1 text-sm">
          <li>✅ Indent buttons should be visible in toolbar</li>
          <li>✅ Selecting text and clicking → should add ql-indent-1 class</li>
          <li>✅ Clicking → again should change to ql-indent-2</li>
          <li>✅ Clicking ← should reduce indent level</li>
          <li>✅ Indented content should appear shifted right in preview</li>
        </ul>
      </div>
    </div>
  );
}
