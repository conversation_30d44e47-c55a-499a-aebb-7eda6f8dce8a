import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { RefreshCw, Crown, Star, Zap, Users, Mail, Clock } from 'lucide-react';

interface ExpiringUser {
  user_id: string;
  email: string | null;
  user_type: string;
  days_until_expiry: number;
}

interface DowngradedUser {
  user_id: string;
  email: string | null;
  user_type: string;
  downgraded_at: string;
}

interface AllUser {
  user_id: string;
  email: string | null;
  user_type: string;
  subscription_status: string;
  subscription_start_date: string | null;
  subscription_end_date: string | null;
  days_until_expiry: number | null;
  is_expired: boolean;
  is_super_admin: boolean;
  is_lifetime: boolean;
}

export default function SubscriptionManager() {
  const [expiringUsers, setExpiringUsers] = useState<ExpiringUser[]>([]);
  const [downgradedUsers, setDowngradedUsers] = useState<DowngradedUser[]>([]);
  const [allUsers, setAllUsers] = useState<AllUser[]>([]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);
  const [lastScheduledCheck, setLastScheduledCheck] = useState<Date | null>(null);
  const [scheduledCheckLoading, setScheduledCheckLoading] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    await Promise.all([
      loadExpiringUsers(),
      loadDowngradedUsers(),
      loadLastScheduledCheck(),
      loadAllUsers()
    ]);
  };

  const loadAllUsers = async () => {
    try {
      const { data, error } = await supabase
        .rpc('get_all_users_subscription_status');
      
      if (error) throw error;
      
      setAllUsers(data || []);
    } catch (error) {
      console.error('Error loading all users:', error);
    }
  };

  const loadLastScheduledCheck = async () => {
    try {
      const { data, error } = await supabase
        .rpc('get_last_scheduled_check');
      
      if (error) throw error;
      
      if (data && data.length > 0) {
        setLastScheduledCheck(new Date(data[0].last_execution));
      }
    } catch (error) {
      console.error('Error loading last scheduled check:', error);
    }
  };

  const triggerScheduledCheck = async () => {
    setScheduledCheckLoading(true);
    try {
      const { data, error } = await supabase
        .rpc('trigger_scheduled_subscription_check');
      
      if (error) throw error;
      
      setMessage({ type: 'success', text: 'Scheduled check triggered successfully! Check the logs for details.' });
      setLastScheduledCheck(new Date());
      
      // Reload data to show updated status
      await loadData();
    } catch (error: any) {
      console.error('Error triggering scheduled check:', error);
      setMessage({ type: 'error', text: `Failed to trigger scheduled check: ${error.message}` });
    } finally {
      setScheduledCheckLoading(false);
    }
  };

  const setUserExpiry = async (userId: string, expiryDate: string) => {
    try {
      const { data, error } = await supabase
        .rpc('set_user_subscription_expiry', { 
          target_user_id: userId, 
          expiry_date: expiryDate 
        });
      
      if (error) throw error;
      
      setMessage({ type: 'success', text: `User expiry set successfully: ${data}` });
      
      // Reload data to show updated status
      await loadData();
    } catch (error: any) {
      console.error('Error setting user expiry:', error);
      setMessage({ type: 'error', text: `Failed to set user expiry: ${error.message}` });
    }
  };

  const upgradeToLifetime = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .rpc('upgrade_user_to_lifetime', { 
          user_id_param: userId
        });
      
      if (error) throw error;
      
      setMessage({ type: 'success', text: `User upgraded to lifetime successfully: ${data}` });
      
      // Reload data to show updated status
      await loadData();
    } catch (error: any) {
      console.error('Error upgrading user to lifetime:', error);
      setMessage({ type: 'error', text: `Failed to upgrade user to lifetime: ${error.message}` });
    }
  };

  const loadExpiringUsers = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/subscription-manager`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        },
        body: JSON.stringify({ action: 'get_expiring_soon', days_ahead: 30 })
      });

      const result = await response.json();
      
      if (result.success) {
        setExpiringUsers(result.expiring_users || []);
      } else {
        throw new Error(result.error || 'Failed to load expiring users');
      }
    } catch (error: any) {
      console.error('Error loading expiring users:', error);
      setMessage({ type: 'error', text: `Failed to load expiring users: ${error.message}` });
    }
  };

  const loadDowngradedUsers = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/subscription-manager`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        },
        body: JSON.stringify({ action: 'get_recently_downgraded', days_back: 30 })
      });

      const result = await response.json();
      
      if (result.success) {
        setDowngradedUsers(result.downgraded_users || []);
      } else {
        throw new Error(result.error || 'Failed to load downgraded users');
      }
    } catch (error: any) {
      console.error('Error loading downgraded users:', error);
      setMessage({ type: 'error', text: `Failed to load downgraded users: ${error.message}` });
    }
  };

  const checkExpiredSubscriptions = async () => {
    setLoading(true);
    try {
      console.log('Calling subscription-manager Edge Function...');
      
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/subscription-manager`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        },
        body: JSON.stringify({ action: 'check_expired_subscriptions' })
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));

      const result = await response.json();
      console.log('Response result:', result);
      
      if (result.success) {
        setMessage({ type: 'success', text: result.message });
        setLastChecked(new Date());
        // Reload data to show updated status
        await loadData();
      } else {
        console.error('Edge Function returned error:', result);
        throw new Error(result.error || 'Failed to check expired subscriptions');
      }
    } catch (error: any) {
      console.error('Error checking expired subscriptions:', error);
      console.error('Full error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
      setMessage({ type: 'error', text: `Failed to check expired subscriptions: ${error.message}` });
    } finally {
      setLoading(false);
    }
  };

  const sendExpiryNotifications = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/subscription-manager`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        },
        body: JSON.stringify({ action: 'send_expiry_notifications' })
      });

      const result = await response.json();
      
      if (result.success) {
        setMessage({ type: 'success', text: `Sent ${result.notifications_sent} expiry notifications` });
      } else {
        throw new Error(result.error || 'Failed to send notifications');
      }
    } catch (error) {
      console.error('Error sending notifications:', error);
      setMessage({ type: 'error', text: 'Failed to send expiry notifications' });
    } finally {
      setLoading(false);
    }
  };

  const getPlanIcon = (userType: string) => {
    switch (userType) {
      case 'super_admin':
        return <Crown className="w-4 h-4 text-red-600" />;
      case 'lifetime':
        return <Crown className="w-4 h-4 text-green-600" />;
      case 'super_unlimited':
        return <Crown className="w-4 h-4 text-yellow-600" />;
      case 'unlimited_yearly':
        return <Star className="w-4 h-4 text-blue-600" />;
      case 'unlimited_monthly':
        return <Zap className="w-4 h-4 text-green-600" />;
      case 'premium':
        return <Crown className="w-4 h-4 text-blue-500" />;
      case 'unlimited':
        return <Zap className="w-4 h-4 text-purple-500" />;
      case 'free':
        return <Star className="w-4 h-4 text-gray-500" />;
      default:
        return <Users className="w-4 h-4 text-gray-600" />;
    }
  };

  const getExpiryColor = (days: number) => {
    if (days <= 3) return 'text-red-600 bg-red-100';
    if (days <= 7) return 'text-orange-600 bg-orange-100';
    if (days <= 14) return 'text-yellow-600 bg-yellow-100';
    return 'text-blue-600 bg-blue-100';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Subscription Management</h2>
          <p className="text-gray-600">Monitor and manage user subscription status</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={triggerScheduledCheck}
            disabled={scheduledCheckLoading}
            className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
            title="Trigger the full scheduled subscription check (downgrades + notifications)"
          >
            <Clock className={`w-4 h-4 mr-2 ${scheduledCheckLoading ? 'animate-spin' : ''}`} />
            {scheduledCheckLoading ? 'Running...' : 'Run Scheduled Check'}
          </button>
          <button
            onClick={checkExpiredSubscriptions}
            disabled={loading}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Check Expired
          </button>
          <button
            onClick={sendExpiryNotifications}
            disabled={loading}
            className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
          >
            <Mail className="w-4 h-4 mr-2" />
            Send Notifications
          </button>
        </div>
      </div>

      {/* Status Message */}
      {message && (
        <div className={`p-4 rounded-lg ${
          message.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {message.text}
        </div>
      )}

      {/* Last Checked */}
      {lastChecked && (
        <div className="text-sm text-gray-500">
          Last checked: {lastChecked.toLocaleString()}
        </div>
      )}

      {/* Expiring Users Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <Clock className="w-5 h-5 text-orange-500 mr-2" />
              Expiring Subscriptions ({expiringUsers.length})
            </h3>
            <button
              onClick={loadExpiringUsers}
              disabled={loading}
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              Refresh
            </button>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          {expiringUsers.length === 0 ? (
            <div className="px-6 py-8 text-center text-gray-500">
              No subscriptions expiring in the next 30 days
            </div>
          ) : (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Plan
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Expires
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Days Left
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {expiringUsers.map((user) => (
                  <tr key={user.user_id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{user.email || 'N/A'}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getPlanIcon(user.user_type)}
                        <span className="ml-2 text-sm text-gray-900 capitalize">
                          {user.user_type ? user.user_type.replace('_', ' ') : 'N/A'}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.days_until_expiry} days until expiry
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>

      {/* Recently Downgraded Users Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <Clock className="w-5 h-5 text-gray-500 mr-2" />
              Recently Downgraded ({downgradedUsers.length})
            </h3>
            <button
              onClick={loadDowngradedUsers}
              disabled={loading}
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              Refresh
            </button>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          {downgradedUsers.length === 0 ? (
            <div className="px-6 py-8 text-center text-gray-500">
              No recently downgraded users
            </div>
          ) : (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Previous Plan
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Downgraded
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Days Ago
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {downgradedUsers.map((user) => (
                  <tr key={user.user_id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{user.email || 'N/A'}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getPlanIcon(user.user_type)}
                        <span className="ml-2 text-sm text-gray-900 capitalize">
                          {user.user_type ? user.user_type.replace('_', ' ') : 'N/A'}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(user.downgraded_at)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                        {user.downgraded_at ? Math.floor((Date.now() - new Date(user.downgraded_at).getTime()) / (1000 * 60 * 60 * 24)) : 0} days
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>

      {/* All Users Subscription Status */}
      <div className="mt-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <Users className="w-5 h-5 text-blue-500 mr-2" />
              All Users Subscription Status ({allUsers.length})
            </h3>
            <button
              onClick={loadAllUsers}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              Refresh
            </button>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Plan
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Start Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    End Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Days Left
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {allUsers.map((user, index) => (
                  <tr key={user.user_id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {user.email}
                      </div>
                      <div className="text-sm text-gray-500">
                        {user.user_id.slice(0, 8)}...
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          user.user_type === 'free' ? 'bg-gray-100 text-gray-800' :
                          user.user_type === 'premium' ? 'bg-blue-100 text-blue-800' :
                          user.user_type === 'unlimited' ? 'bg-purple-100 text-purple-800' :
                          user.user_type === 'super_admin' ? 'bg-red-100 text-red-800' :
                          user.user_type === 'lifetime' ? 'bg-green-100 text-green-800' :
                          'bg-blue-100 text-blue-800'
                        }`}>
                          {user.user_type ? user.user_type.replace('_', ' ').toUpperCase() : 'N/A'}
                        </span>
                        {user.is_super_admin && (
                          <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                            👑 SUPER ADMIN
                          </span>
                        )}
                        {user.is_lifetime && (
                          <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                            ♾️ LIFETIME
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        user.subscription_status === 'active' ? 'bg-green-100 text-green-800' :
                        user.subscription_status === 'expired' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {user.subscription_status || 'N/A'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.subscription_start_date ? new Date(user.subscription_start_date).toLocaleDateString() : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`text-sm ${
                        user.is_expired ? 'text-red-600 font-medium' :
                        user.days_until_expiry && user.days_until_expiry <= 3 ? 'text-orange-600 font-medium' :
                        'text-gray-900'
                      }`}>
                        {user.subscription_end_date ? new Date(user.subscription_end_date).toLocaleDateString() : 'N/A'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.is_super_admin ? (
                        <span className="text-green-600 font-medium">♾️ PERMANENT</span>
                      ) : user.is_lifetime ? (
                        <span className="text-green-600 font-medium">♾️ LIFETIME</span>
                      ) : user.days_until_expiry !== null ? (
                        <span className={`${
                          user.days_until_expiry < 0 ? 'text-red-600 font-medium' :
                          user.days_until_expiry <= 3 ? 'text-orange-600 font-medium' :
                          'text-gray-900'
                        }`}>
                          {user.days_until_expiry < 0 ? 
                            `${Math.abs(user.days_until_expiry)} days overdue` : 
                            `${user.days_until_expiry} days left`
                          }
                        </span>
                      ) : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {!user.is_super_admin && !user.is_lifetime && (
                        <button
                          onClick={() => upgradeToLifetime(user.user_id)}
                          className="inline-flex items-center px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full hover:bg-green-200 transition-colors"
                          title="Upgrade user to lifetime plan - permanent access"
                        >
                          ♾️ Upgrade to Lifetime
                        </button>
                      )}
                      {(user.is_super_admin || user.is_lifetime) && (
                        <span className="text-gray-400 text-xs">No actions needed</span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Clock className="w-6 h-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Expiring Soon</p>
              <p className="text-2xl font-semibold text-gray-900">{expiringUsers.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-gray-100 rounded-lg">
              <Clock className="w-6 h-6 text-gray-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Recently Downgraded</p>
              <p className="text-2xl font-semibold text-gray-900">{downgradedUsers.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <RefreshCw className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Last Check</p>
              <p className="text-2xl font-semibold text-gray-900">
                {lastChecked ? '✓ Done' : 'Not Run'}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Clock className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Last Scheduled Check</p>
              <p className="text-2xl font-semibold text-gray-900">
                {lastScheduledCheck ? '✓ Done' : 'Not Run'}
              </p>
              {lastScheduledCheck && (
                <p className="text-sm text-gray-500">
                  {lastScheduledCheck.toLocaleDateString()} at {lastScheduledCheck.toLocaleTimeString()}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
