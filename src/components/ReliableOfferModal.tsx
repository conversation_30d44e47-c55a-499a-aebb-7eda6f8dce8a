import React, { useState, useEffect } from 'react';
import { X, Save, Palette, Eye } from 'lucide-react';
import TinyMCEEditor from './TinyMCEEditor';
import { Offer, LandingPage } from '../types';
import CoverImageUpload from './CoverImageUpload';

interface OfferModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (offer: Partial<Offer>) => void;
  offer?: Offer | null;
  mode: 'create' | 'edit';
}

export default function ReliableOfferModal({ isOpen, onClose, onSave, offer, mode }: OfferModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    buttonText: 'Learn More',
    landingPage: {
      id: 'dummy',
      title: '',
      subtitle: '',
      content: '',
      ctaText: 'Get Started',
      ctaUrl: '',
      backgroundColor: '#ffffff',
      textColor: '#000000',
      ctaButtonColor: '#2563eb',
      ctaButtonTextColor: '#ffffff',
      headerImageUrl: '',
    }
  });

  const [showPreview, setShowPreview] = useState(false);

  useEffect(() => {
    if (offer && mode === 'edit') {
      setFormData({
        title: offer.title || '',
        description: offer.description || '',
        buttonText: offer.buttonText || 'Learn More',
        landingPage: {
          id: offer.landingPage?.id || 'dummy',
          title: offer.landingPage?.title || '',
          subtitle: offer.landingPage?.subtitle || '',
          content: offer.landingPage?.content || '',
          ctaText: offer.landingPage?.ctaText || 'Get Started',
          ctaUrl: offer.landingPage?.ctaUrl || '',
          backgroundColor: offer.landingPage?.backgroundColor || '#ffffff',
          textColor: offer.landingPage?.textColor || '#000000',
          ctaButtonColor: offer.landingPage?.ctaButtonColor || '#2563eb',
          ctaButtonTextColor: offer.landingPage?.ctaButtonTextColor || '#ffffff',
          headerImageUrl: offer.landingPage?.headerImageUrl || '',
        }
      });
    } else {
      // Reset form for create mode
      setFormData({
        title: '',
        description: '',
        buttonText: 'Learn More',
        landingPage: {
          id: 'dummy',
          title: '',
          subtitle: '',
          content: '',
          ctaText: 'Get Started',
          ctaUrl: '',
          backgroundColor: '#ffffff',
          textColor: '#000000',
          ctaButtonColor: '#2563eb',
          ctaButtonTextColor: '#ffffff',
          headerImageUrl: '',
        }
      });
    }
  }, [offer, mode, isOpen]);

  const updateLandingPage = (field: keyof LandingPage, value: any) => {
    setFormData(prev => ({
      ...prev,
      landingPage: {
        ...prev.landingPage,
        [field]: value
      }
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const offerData: Partial<Offer> = {
      title: formData.title,
      description: formData.description,
      buttonText: formData.buttonText,
      landingPage: formData.landingPage,
      isActive: true,
    };

    if (mode === 'edit' && offer) {
      offerData.id = offer.id;
    }

    onSave(offerData);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-7xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200">
          <h2 className="text-xl sm:text-2xl font-bold text-gray-900">
            {mode === 'create' ? 'Create New Offer' : 'Edit Offer'} (Reliable Editor)
          </h2>
          <div className="flex items-center space-x-2">
            <button
              type="button"
              onClick={() => setShowPreview(!showPreview)}
              className="flex items-center px-3 sm:px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors text-sm sm:text-base"
            >
              <Eye className="w-4 h-4 mr-2" />
              {showPreview ? 'Hide Preview' : 'Show Preview'}
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row h-[calc(90vh-80px)]">
          {/* Form Section */}
          <div className={`${showPreview ? 'lg:w-1/2' : 'w-full'} overflow-y-auto`}>
            <form onSubmit={handleSubmit} className="p-4 sm:p-6 space-y-6">
              {/* Offer Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Offer Details</h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Offer Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="e.g., Free Consultation"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description *
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    required
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    placeholder="Brief description of your offer..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Button Text
                  </label>
                  <input
                    type="text"
                    value={formData.buttonText}
                    onChange={(e) => setFormData(prev => ({ ...prev, buttonText: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="e.g., Learn More, Get Started"
                  />
                </div>
              </div>

              {/* Landing Page Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                  <Palette className="w-5 h-5 mr-2 text-blue-500" />
                  Landing Page
                </h3>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Page Title *
                    </label>
                    <input
                      type="text"
                      value={formData.landingPage.title}
                      onChange={(e) => updateLandingPage('title', e.target.value)}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Main headline for your landing page"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Subtitle
                    </label>
                    <input
                      type="text"
                      value={formData.landingPage.subtitle}
                      onChange={(e) => updateLandingPage('subtitle', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Supporting text under the title"
                    />
                  </div>
                </div>

                {/* TinyMCE Editor for Content */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Content * (Reliable Editor)
                  </label>
                  <TinyMCEEditor
                    value={formData.landingPage.content}
                    onChange={(content) => updateLandingPage('content', content)}
                    placeholder="Write your landing page content here. Bullets and indentation work perfectly!"
                    height={250}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    ✅ Bullets work perfectly • Indentation works • No styling issues
                  </p>
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors font-medium"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex items-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-2 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium shadow-lg transform hover:scale-105"
                >
                  <Save className="w-4 h-4 mr-2" />
                  {mode === 'create' ? 'Create Offer' : 'Save Changes'}
                </button>
              </div>
            </form>
          </div>

          {/* Preview Section */}
          {showPreview && (
            <div className="w-full lg:w-1/2 border-l border-gray-200 bg-gray-50">
              <div className="p-4 border-b border-gray-200">
                <h3 className="font-semibold text-gray-900">Live Preview</h3>
              </div>
              <div className="p-4 overflow-y-auto h-full">
                {/* Offer Preview */}
                <div className="bg-white rounded-lg p-4 mb-4 shadow-sm">
                  <h4 className="font-semibold text-gray-800 mb-2">{formData.title || 'Offer Title'}</h4>
                  <p className="text-sm text-gray-600 mb-3">{formData.description || 'Offer description...'}</p>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                    {formData.buttonText}
                  </button>
                </div>

                {/* Landing Page Preview */}
                <div 
                  className="rounded-lg p-4 sm:p-6 min-h-[400px]"
                  style={{ 
                    backgroundColor: formData.landingPage.backgroundColor,
                    color: formData.landingPage.textColor 
                  }}
                >
                  <h1 className="text-xl sm:text-2xl font-bold mb-2">
                    {formData.landingPage.title || 'Landing Page Title'}
                  </h1>
                  <p className="text-base sm:text-lg mb-4 opacity-90">
                    {formData.landingPage.subtitle || 'Subtitle goes here'}
                  </p>

                  {/* Rich Content */}
                  <div 
                    className="mb-6 prose prose-sm max-w-none"
                    style={{ color: formData.landingPage.textColor }}
                    dangerouslySetInnerHTML={{ 
                      __html: formData.landingPage.content || '<p>Your rich content will appear here...</p>' 
                    }}
                  />

                  <button
                    className="px-6 py-3 rounded-lg font-medium transition-colors"
                    style={{
                      backgroundColor: formData.landingPage.ctaButtonColor,
                      color: formData.landingPage.ctaButtonTextColor
                    }}
                  >
                    {formData.landingPage.ctaText}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
