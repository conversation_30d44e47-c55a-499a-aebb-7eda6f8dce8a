import React, { useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

export default function IndentationTest() {
  const [content, setContent] = useState(`
    <h2>Indentation Test</h2>
    <p>Test the indent functionality with these examples:</p>
    <p>Normal paragraph</p>
    <p class="ql-indent-1">Indented paragraph level 1</p>
    <p class="ql-indent-2">Indented paragraph level 2</p>
    <p class="ql-indent-3">Indented paragraph level 3</p>
    <ul>
      <li>Normal bullet</li>
      <li class="ql-indent-1">Indented bullet level 1</li>
      <li class="ql-indent-2">Indented bullet level 2</li>
      <li>Back to normal</li>
    </ul>
    <ol>
      <li>Normal number</li>
      <li class="ql-indent-1">Indented number level 1</li>
      <li class="ql-indent-2">Indented number level 2</li>
      <li>Back to normal</li>
    </ol>
  `);

  // Quill configuration with indent functionality
  const quillModules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }], // These are the indent buttons
      ['link'],
      ['clean']
    ],
    clipboard: {
      matchVisual: false,
    }
  };

  const quillFormats = [
    'header', 'bold', 'italic', 'underline',
    'list', 'bullet', 'ordered', 'indent', 'link'
  ];

  const loadTestContent = () => {
    setContent(`
      <h2>Indentation Test Content</h2>
      <p>Select text below and use the indent buttons (→ and ←) in the toolbar:</p>
      <p>This is a normal paragraph</p>
      <p>This paragraph should be indented when you select it and click →</p>
      <p>This paragraph can be indented multiple times</p>
      <ul>
        <li>Normal bullet point</li>
        <li>This bullet can be indented</li>
        <li>This bullet can be indented even more</li>
      </ul>
      <ol>
        <li>Normal numbered item</li>
        <li>This number can be indented</li>
        <li>This number can be indented even more</li>
      </ol>
      <p><strong>Instructions:</strong></p>
      <ol>
        <li>Select any paragraph or list item</li>
        <li>Click the → button to indent right</li>
        <li>Click the ← button to indent left</li>
        <li>Check the preview to see if indentation appears</li>
      </ol>
    `);
  };

  const clearContent = () => {
    setContent('');
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Indentation Functionality Test</h1>
        <p className="text-gray-600 mb-4">
          Test if the indent buttons (→ and ←) work properly in the editor and display correctly in the preview.
        </p>
        <div className="flex gap-4 mb-4">
          <button
            onClick={loadTestContent}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Load Test Content
          </button>
          <button
            onClick={clearContent}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Clear
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Editor */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-2">Editor</h2>
          <p className="text-sm text-gray-600 mb-4">
            Select text and use the → and ← buttons in the toolbar to test indentation.
          </p>
          <div className="border border-gray-300 rounded-lg overflow-hidden">
            <ReactQuill
              theme="snow"
              value={content}
              onChange={setContent}
              modules={quillModules}
              formats={quillFormats}
              placeholder="Type here and test indentation..."
              style={{ minHeight: '400px' }}
            />
          </div>
        </div>

        {/* Landing Page Preview */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-2">Landing Page Preview</h2>
          <p className="text-sm text-gray-600 mb-4">
            This shows how the content will look on your landing page.
          </p>
          <div className="border border-gray-300 rounded-lg p-6 bg-white">
            <div 
              className="prose prose-sm max-w-none landing-content"
              dangerouslySetInnerHTML={{ __html: content }}
            />
          </div>
        </div>
      </div>

      {/* Raw HTML Output */}
      <div className="mt-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Raw HTML Output</h3>
        <p className="text-sm text-gray-600 mb-2">
          Check if ql-indent-1, ql-indent-2, etc. classes are being added to indented elements.
        </p>
        <pre className="bg-gray-100 p-4 rounded-lg text-xs overflow-auto max-h-60">
          {content || 'No content'}
        </pre>
      </div>

      {/* Instructions */}
      <div className="mt-6 bg-blue-50 p-4 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">How to Test Indentation:</h3>
        <ol className="text-blue-700 space-y-1 text-sm list-decimal list-inside">
          <li>Click in a paragraph or list item in the editor</li>
          <li>Look for the → and ← buttons in the toolbar (they should be visible)</li>
          <li>Click the → button to indent the selected text to the right</li>
          <li>Click the ← button to indent the selected text to the left</li>
          <li>Check the "Landing Page Preview" to see if indentation appears</li>
          <li>Check the "Raw HTML Output" to see if ql-indent-X classes are added</li>
        </ol>
      </div>

      {/* Troubleshooting */}
      <div className="mt-4 bg-yellow-50 p-4 rounded-lg">
        <h3 className="font-semibold text-yellow-800 mb-2">If Indentation Doesn't Work:</h3>
        <ul className="text-yellow-700 space-y-1 text-sm">
          <li>• Make sure you have text selected or cursor positioned in a paragraph/list item</li>
          <li>• Look for the → and ← buttons in the toolbar (they might be grayed out if not applicable)</li>
          <li>• Check if ql-indent-X classes appear in the HTML output</li>
          <li>• Try with different types of content (paragraphs, lists, etc.)</li>
        </ul>
      </div>

      {/* Expected Results */}
      <div className="mt-4 bg-green-50 p-4 rounded-lg">
        <h3 className="font-semibold text-green-800 mb-2">Expected Results:</h3>
        <ul className="text-green-700 space-y-1 text-sm">
          <li>✅ Indent buttons should be visible in the toolbar</li>
          <li>✅ Clicking → should move content to the right</li>
          <li>✅ Clicking ← should move content to the left</li>
          <li>✅ Indented content should show in the landing page preview</li>
          <li>✅ HTML should contain class="ql-indent-1", class="ql-indent-2", etc.</li>
        </ul>
      </div>
    </div>
  );
}
