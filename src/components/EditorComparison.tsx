import React, { useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import TinyMCEEditor from './TinyMCEEditor';
import LexicalEditor from './LexicalEditor';

export default function EditorComparison() {
  const [reactQuillContent, setReactQuillContent] = useState('');
  const [tinyMCEContent, setTinyMCEContent] = useState('');
  const [lexicalContent, setLexicalContent] = useState('');

  // ReactQuill configuration (improved)
  const quillModules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'align': [] }],
      ['link', 'image'],
      ['blockquote', 'code-block'],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      ['clean']
    ],
    clipboard: {
      matchVisual: false,
    }
  };

  const quillFormats = [
    'header', 'bold', 'italic', 'underline', 'strike',
    'color', 'background', 'list', 'bullet', 'ordered',
    'align', 'link', 'image', 'blockquote', 'code-block', 'indent'
  ];

  const sampleContent = `
    <h2>Sample Content</h2>
    <p>This is a paragraph with <strong>bold text</strong> and <em>italic text</em>.</p>
    <ul>
      <li>First bullet point</li>
      <li>Second bullet point with <u>underlined text</u></li>
      <li>Third bullet point</li>
    </ul>
    <ol>
      <li>First numbered item</li>
      <li>Second numbered item</li>
    </ol>
    <p>Here's a <a href="https://example.com">link</a> and some more text.</p>
  `;

  const loadSampleContent = () => {
    setReactQuillContent(sampleContent);
    setTinyMCEContent(sampleContent);
    setLexicalContent(sampleContent);
  };

  const clearContent = () => {
    setReactQuillContent('');
    setTinyMCEContent('');
    setLexicalContent('');
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Rich Text Editor Comparison</h1>
        <p className="text-gray-600 mb-4">
          Compare the three editor options: ReactQuill (improved), TinyMCE, and Lexical
        </p>
        <div className="flex gap-4 mb-6">
          <button
            onClick={loadSampleContent}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Load Sample Content
          </button>
          <button
            onClick={clearContent}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Clear All
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* ReactQuill (Improved) */}
        <div className="space-y-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">ReactQuill (Improved)</h2>
            <p className="text-sm text-gray-600 mb-4">
              Your current editor with fixes for bullet points and styling
            </p>
          </div>
          <div className="border border-gray-300 rounded-lg overflow-hidden">
            <ReactQuill
              theme="snow"
              value={reactQuillContent}
              onChange={setReactQuillContent}
              modules={quillModules}
              formats={quillFormats}
              placeholder="Type here with ReactQuill..."
              style={{ minHeight: '200px' }}
            />
          </div>
          <div className="text-xs text-gray-500">
            <strong>Pros:</strong> Lightweight, familiar, good for basic editing<br/>
            <strong>Cons:</strong> Limited customization, some styling issues
          </div>
        </div>

        {/* TinyMCE */}
        <div className="space-y-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">TinyMCE</h2>
            <p className="text-sm text-gray-600 mb-4">
              Industry-standard WYSIWYG editor with excellent reliability
            </p>
          </div>
          <TinyMCEEditor
            value={tinyMCEContent}
            onChange={setTinyMCEContent}
            placeholder="Type here with TinyMCE..."
            height={250}
          />
          <div className="text-xs text-gray-500">
            <strong>Pros:</strong> Very reliable, excellent formatting, great bullet support<br/>
            <strong>Cons:</strong> Larger bundle size, requires API key for full features
          </div>
        </div>

        {/* Lexical */}
        <div className="space-y-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Lexical</h2>
            <p className="text-sm text-gray-600 mb-4">
              Facebook's modern editor framework with excellent performance
            </p>
          </div>
          <LexicalEditor
            value={lexicalContent}
            onChange={setLexicalContent}
            placeholder="Type here with Lexical..."
          />
          <div className="text-xs text-gray-500">
            <strong>Pros:</strong> Modern, performant, highly customizable, great mobile support<br/>
            <strong>Cons:</strong> Newer technology, steeper learning curve
          </div>
        </div>
      </div>

      {/* Content Output */}
      <div className="mt-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Generated HTML Output</h3>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div>
            <h4 className="font-medium text-gray-700 mb-2">ReactQuill Output:</h4>
            <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-40">
              {reactQuillContent || 'No content'}
            </pre>
          </div>
          <div>
            <h4 className="font-medium text-gray-700 mb-2">TinyMCE Output:</h4>
            <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-40">
              {tinyMCEContent || 'No content'}
            </pre>
          </div>
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Lexical Output:</h4>
            <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-40">
              {lexicalContent || 'No content'}
            </pre>
          </div>
        </div>
      </div>

      {/* Recommendations */}
      <div className="mt-8 p-6 bg-blue-50 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">Recommendations</h3>
        <div className="space-y-2 text-blue-800">
          <p><strong>For immediate fix:</strong> Try the improved ReactQuill configuration first</p>
          <p><strong>For best reliability:</strong> Switch to TinyMCE - it's the most stable option</p>
          <p><strong>For future-proofing:</strong> Consider Lexical for its modern architecture and performance</p>
        </div>
      </div>
    </div>
  );
}
