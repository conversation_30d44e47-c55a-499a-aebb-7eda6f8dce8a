import React, { useEffect, useState } from 'react';
import QRCode from 'qrcode';
import { Download, Share2, Copy, Check } from 'lucide-react';

interface QRCodeGeneratorProps {
  url: string;
  title: string;
  className?: string;
}

export default function QRCodeGenerator({ url, title, className = '' }: QRCodeGeneratorProps) {
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>('');
  const [copied, setCopied] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    generateQRCode();
  }, [url]);

  const generateQRCode = async () => {
    try {
      setLoading(true);
      const qrCodeUrl = await QRCode.toDataURL(url, {
        width: 256,
        margin: 2,
        color: {
          dark: '#1f2937', // Dark gray
          light: '#ffffff' // White
        },
        errorCorrectionLevel: 'M'
      });
      setQrCodeDataUrl(qrCodeUrl);
    } catch (error) {
      console.error('Error generating QR code:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = () => {
    if (!qrCodeDataUrl) return;

    const link = document.createElement('a');
    link.download = `${title.replace(/\s+/g, '-').toLowerCase()}-qr-code.png`;
    link.href = qrCodeDataUrl;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy URL:', error);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${title} - Digital Business Card`,
          text: `Check out ${title}'s digital business card`,
          url: url
        });
      } catch (error) {
        // Handle DOMException errors (NotAllowedError, AbortError, etc.)
        if (error instanceof DOMException) {
          if (error.name === 'NotAllowedError' || error.name === 'AbortError') {
            // User denied permission or cancelled the share dialog
            // Fall back to copying URL
            handleCopyUrl();
          } else {
            console.error('Web Share API error:', error);
            // Fall back to copying URL for other DOMException errors
            handleCopyUrl();
          }
        } else {
          console.error('Error sharing:', error);
          // Fall back to copying URL for any other errors
          handleCopyUrl();
        }
      }
    } else {
      // Fallback to copying URL
      handleCopyUrl();
    }
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-xl shadow-lg p-6 ${className}`}>
        <div className="text-center">
          <div className="w-32 h-32 bg-gray-200 rounded-lg mx-auto mb-4 animate-pulse"></div>
          <p className="text-gray-500">Generating QR code...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-xl shadow-lg p-6 ${className}`}>
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Share Your Card</h3>
        
        {/* QR Code */}
        <div className="bg-white p-4 rounded-lg border-2 border-gray-200 inline-block mb-4">
          {qrCodeDataUrl && (
            <img 
              src={qrCodeDataUrl} 
              alt="QR Code"
              className="w-32 h-32 mx-auto"
            />
          )}
        </div>

        <p className="text-sm text-gray-600 mb-4">
          Scan this QR code to view the business card
        </p>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-2 justify-center">
          <button
            onClick={handleDownload}
            className="flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
          >
            <Download className="w-4 h-4 mr-2" />
            Download QR
          </button>
          
          <button
            onClick={handleShare}
            className="flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
          >
            <Share2 className="w-4 h-4 mr-2" />
            Share Card
          </button>
          
          <button
            onClick={handleCopyUrl}
            className={`flex items-center justify-center px-4 py-2 rounded-lg transition-colors text-sm font-medium ${
              copied 
                ? 'bg-green-100 text-green-700 border border-green-300' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300'
            }`}
          >
            {copied ? (
              <>
                <Check className="w-4 h-4 mr-2" />
                Copied!
              </>
            ) : (
              <>
                <Copy className="w-4 h-4 mr-2" />
                Copy Link
              </>
            )}
          </button>
        </div>

        {/* URL Display */}
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <p className="text-xs text-gray-500 mb-1">Card URL:</p>
          <p className="text-sm text-gray-700 font-mono break-all">{url}</p>
        </div>
      </div>
    </div>
  );
}