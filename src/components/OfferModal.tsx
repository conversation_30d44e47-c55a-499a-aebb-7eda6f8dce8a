import React, { useState, useEffect } from 'react';
import { X, Save, Palette, Eye } from 'lucide-react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { Offer, LandingPage } from '../types';
import CoverImageUpload from './CoverImageUpload';

interface OfferModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (offer: Partial<Offer>) => void;
  offer?: Offer | null;
  mode: 'create' | 'edit';
}

export default function OfferModal({ isOpen, onClose, onSave, offer, mode }: OfferModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    buttonText: 'Learn More',
    landingPage: {
      id: 'dummy',
      title: '',
      subtitle: '',
      content: '',
      ctaText: 'Get Started',
      ctaUrl: '',
      backgroundColor: '#ffffff',
      textColor: '#000000',
      ctaButtonColor: '#2563eb',
      ctaButtonTextColor: '#ffffff',
      headerImageUrl: '',
    }
  });

  const [showPreview, setShowPreview] = useState(false);

  // Quill editor configuration
  const quillModules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'align': [] }],
      ['link', 'image'],
      ['blockquote', 'code-block'],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      ['clean']
    ],
  };

  const quillFormats = [
    'header', 'bold', 'italic', 'underline', 'strike',
    'color', 'background', 'list', 'bullet', 'align',
    'link', 'image', 'blockquote', 'code-block', 'indent'
  ];

  useEffect(() => {
    if (offer && mode === 'edit') {
      setFormData({
        title: offer.title,
        description: offer.description,
        buttonText: offer.buttonText,
        landingPage: {
          id: offer.landingPage.id || 'dummy',
          ...offer.landingPage,
          ctaButtonColor: offer.landingPage.ctaButtonColor || '#2563eb',
          ctaButtonTextColor: offer.landingPage.ctaButtonTextColor || '#ffffff',
          headerImageUrl: offer.landingPage.headerImageUrl || '',
        }
      });
    } else if (mode === 'create') {
      setFormData({
        title: '',
        description: '',
        buttonText: 'Learn More',
        landingPage: {
          id: 'dummy',
          title: '',
          subtitle: '',
          content: '',
          ctaText: 'Get Started',
          ctaUrl: '',
          backgroundColor: '#ffffff',
          textColor: '#000000',
          ctaButtonColor: '#2563eb',
          ctaButtonTextColor: '#ffffff',
          headerImageUrl: '',
        }
      });
    }
  }, [offer, mode, isOpen]);

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
    onClose();
  };

  const updateLandingPage = (field: keyof LandingPage, value: string) => {
    setFormData(prev => ({
      ...prev,
      landingPage: {
        ...prev.landingPage,
        [field]: value
      }
    }));
  };

  const backgroundColors = [
    '#ffffff', '#f8fafc', '#f1f5f9', '#e2e8f0', '#cbd5e1',
    '#4567b7', '#2563eb', '#7c3aed', '#dc2626', '#059669', 
    '#ea580c', '#be185d', '#0891b2', '#4338ca', '#7c2d12'
  ];

  const buttonColors = [
    '#2563eb', '#7c3aed', '#dc2626', '#059669', '#ea580c',
    '#be185d', '#0891b2', '#4338ca', '#7c2d12', '#1f2937',
    '#ffffff', '#f3f4f6'
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-7xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200">
          <h2 className="text-xl sm:text-2xl font-bold text-gray-900">
            {mode === 'create' ? 'Create New Offer' : 'Edit Offer'}
          </h2>
          <div className="flex items-center space-x-2">
            <button
              type="button"
              onClick={() => setShowPreview(!showPreview)}
              className="flex items-center px-3 sm:px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors text-sm sm:text-base"
            >
              <Eye className="w-4 h-4 mr-2" />
              <span className="hidden sm:inline">{showPreview ? 'Hide Preview' : 'Show Preview'}</span>
              <span className="sm:hidden">{showPreview ? 'Hide' : 'Preview'}</span>
            </button>
            <button
              onClick={onClose}
              className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
            >
              <X className="w-5 h-5 text-gray-600" />
            </button>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row h-[calc(90vh-80px)]">
          {/* Form Section */}
          <div className={`${showPreview ? 'lg:w-1/2' : 'w-full'} overflow-y-auto`}>
            <form onSubmit={handleSubmit} className="p-4 sm:p-6 space-y-6">
              {/* Offer Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Offer Details</h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Offer Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="e.g., Free Consultation"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description *
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    required
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    placeholder="Describe your offer in detail..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Button Text
                  </label>
                  <input
                    type="text"
                    value={formData.buttonText}
                    onChange={(e) => setFormData(prev => ({ ...prev, buttonText: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="e.g., Learn More, Get Started"
                  />
                </div>
              </div>

              {/* Landing Page */}
              <div className="space-y-4 border-t border-gray-200 pt-6">
                <h3 className="text-lg font-semibold text-gray-900">Landing Page</h3>
                
                {/* Header Image Upload */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Header Photo
                  </label>
                  <CoverImageUpload
                    currentImage={formData.landingPage.headerImageUrl}
                    onImageChange={(img) => updateLandingPage('headerImageUrl', img || '')}
                    canUpload={true}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Landing Page Title *
                  </label>
                  <input
                    type="text"
                    value={formData.landingPage.title}
                    onChange={(e) => updateLandingPage('title', e.target.value)}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Main headline for your landing page"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Subtitle
                  </label>
                  <input
                    type="text"
                    value={formData.landingPage.subtitle}
                    onChange={(e) => updateLandingPage('subtitle', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Supporting headline"
                  />
                </div>

                {/* Rich Text Editor for Content */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Content *
                  </label>
                  <div className="border border-gray-300 rounded-lg overflow-hidden">
                    <ReactQuill
                      theme="snow"
                      value={formData.landingPage.content}
                      onChange={(content) => updateLandingPage('content', content)}
                      modules={quillModules}
                      formats={quillFormats}
                      placeholder="Write your landing page content here. You can format text, add images, links, and more..."
                      style={{ minHeight: '200px' }}
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Use the toolbar to format text, add images, links, and create rich content for your landing page.
                  </p>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Call-to-Action Text
                    </label>
                    <input
                      type="text"
                      value={formData.landingPage.ctaText}
                      onChange={(e) => updateLandingPage('ctaText', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="e.g., Get Started, Buy Now"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      CTA URL *
                    </label>
                    <input
                      type="url"
                      value={formData.landingPage.ctaUrl}
                      onChange={(e) => updateLandingPage('ctaUrl', e.target.value)}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="https://example.com"
                    />
                  </div>
                </div>

                {/* Color Customization */}
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 flex items-center">
                    <Palette className="w-4 h-4 mr-2" />
                    Design Customization
                  </h4>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Background Color
                      </label>
                      <div className="flex items-center space-x-3">
                        <input
                          type="color"
                          value={formData.landingPage.backgroundColor}
                          onChange={(e) => updateLandingPage('backgroundColor', e.target.value)}
                          className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                        />
                        <div className="flex flex-wrap gap-2">
                          {backgroundColors.slice(0, 8).map(color => (
                            <button
                              key={color}
                              type="button"
                              onClick={() => updateLandingPage('backgroundColor', color)}
                              className="w-6 h-6 sm:w-8 sm:h-8 rounded-full border-2 border-gray-300 hover:scale-110 transition-transform"
                              style={{ backgroundColor: color }}
                            />
                          ))}
                        </div>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Text Color
                      </label>
                      <div className="flex items-center space-x-3">
                        <input
                          type="color"
                          value={formData.landingPage.textColor}
                          onChange={(e) => updateLandingPage('textColor', e.target.value)}
                          className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                        />
                        <div className="flex gap-2">
                          <button
                            type="button"
                            onClick={() => updateLandingPage('textColor', '#ffffff')}
                            className="w-6 h-6 sm:w-8 sm:h-8 bg-white border border-gray-300 rounded-full hover:scale-110 transition-transform"
                          />
                          <button
                            type="button"
                            onClick={() => updateLandingPage('textColor', '#000000')}
                            className="w-6 h-6 sm:w-8 sm:h-8 bg-black rounded-full hover:scale-110 transition-transform"
                          />
                        </div>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        CTA Button Color
                      </label>
                      <div className="flex items-center space-x-3">
                        <input
                          type="color"
                          value={formData.landingPage.ctaButtonColor}
                          onChange={(e) => updateLandingPage('ctaButtonColor', e.target.value)}
                          className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                        />
                        <div className="flex flex-wrap gap-2">
                          {buttonColors.slice(0, 8).map(color => (
                            <button
                              key={color}
                              type="button"
                              onClick={() => updateLandingPage('ctaButtonColor', color)}
                              className="w-6 h-6 sm:w-8 sm:h-8 rounded-full border-2 border-gray-300 hover:scale-110 transition-transform"
                              style={{ backgroundColor: color }}
                            />
                          ))}
                        </div>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        CTA Button Text Color
                      </label>
                      <div className="flex items-center space-x-3">
                        <input
                          type="color"
                          value={formData.landingPage.ctaButtonTextColor}
                          onChange={(e) => updateLandingPage('ctaButtonTextColor', e.target.value)}
                          className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                        />
                        <div className="flex gap-2">
                          <button
                            type="button"
                            onClick={() => updateLandingPage('ctaButtonTextColor', '#ffffff')}
                            className="w-6 h-6 sm:w-8 sm:h-8 bg-white border border-gray-300 rounded-full hover:scale-110 transition-transform"
                          />
                          <button
                            type="button"
                            onClick={() => updateLandingPage('ctaButtonTextColor', '#000000')}
                            className="w-6 h-6 sm:w-8 sm:h-8 bg-black rounded-full hover:scale-110 transition-transform"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors order-2 sm:order-1"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex items-center justify-center bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors order-1 sm:order-2"
                >
                  <Save className="w-4 h-4 mr-2" />
                  {mode === 'create' ? 'Create Offer' : 'Save Changes'}
                </button>
              </div>
            </form>
          </div>

          {/* Preview Section */}
          {showPreview && (
            <div className="w-full lg:w-1/2 border-l border-gray-200 bg-gray-50">
              <div className="p-4 border-b border-gray-200">
                <h3 className="font-semibold text-gray-900">Live Preview</h3>
              </div>
              <div className="p-4 overflow-y-auto h-full">
                {/* Offer Preview */}
                <div className="bg-white rounded-lg p-4 mb-4 shadow-sm">
                  <h4 className="font-semibold text-gray-800 mb-2">{formData.title || 'Offer Title'}</h4>
                  <p className="text-sm text-gray-600 mb-3">{formData.description || 'Offer description...'}</p>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                    {formData.buttonText}
                  </button>
                </div>

                {/* Landing Page Preview */}
                <div 
                  className="rounded-lg p-4 sm:p-6 min-h-[400px]"
                  style={{ 
                    backgroundColor: formData.landingPage.backgroundColor,
                    color: formData.landingPage.textColor 
                  }}
                >
                  <h1 className="text-xl sm:text-2xl font-bold mb-2">
                    {formData.landingPage.title || 'Landing Page Title'}
                  </h1>
                  <p className="text-base sm:text-lg mb-4 opacity-90">
                    {formData.landingPage.subtitle || 'Subtitle goes here'}
                  </p>

                  {/* Rich Content */}
                  <div 
                    className="mb-6 prose prose-sm max-w-none"
                    style={{ color: formData.landingPage.textColor }}
                    dangerouslySetInnerHTML={{ 
                      __html: formData.landingPage.content || '<p>Your rich content will appear here...</p>' 
                    }}
                  />

                  <button 
                    className="px-4 sm:px-6 py-2 sm:py-3 rounded-lg font-semibold text-sm sm:text-base"
                    style={{
                      backgroundColor: formData.landingPage.ctaButtonColor,
                      color: formData.landingPage.ctaButtonTextColor
                    }}
                  >
                    {formData.landingPage.ctaText}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}