import React from 'react';
import { useParams, Navigate } from 'react-router-dom';
import BusinessCardView from '../pages/BusinessCardView';

// Paths that should NOT be treated as usernames
const EXCLUDED_PATHS = [
  'account',
  'admin',
  'dashboard',
  'contact',
  'privacy',
  'terms',
  'faq',
  'verify-email',
  'subscription-renewal'
];

export default function UsernameRoute() {
  const { username } = useParams<{ username: string }>();
  
  // Debug logging
  console.log('UsernameRoute component loaded');
  console.log('Username param:', username);
  console.log('Current URL:', window.location.href);
  console.log('Excluded paths:', EXCLUDED_PATHS);
  
  // Check if this path should be excluded from username routing
  if (username && EXCLUDED_PATHS.includes(username.toLowerCase())) {
    console.log('Path is excluded, redirecting to home');
    // This path should not be treated as a username, redirect to 404 or home
    return <Navigate to="/" replace />;
  }
  
  console.log('Path is valid username, rendering BusinessCardView');
  // This is a valid username path, render the business card view
  return <BusinessCardView />;
}
