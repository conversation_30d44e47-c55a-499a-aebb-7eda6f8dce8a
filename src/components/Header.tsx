import React from 'react';
import { Settings } from 'lucide-react';

interface HeaderProps {
  onSettingsClick: () => void;
}

export default function Header({ onSettingsClick }: HeaderProps) {
  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <img 
              src="/logo.png" 
              alt="BusinessCardHub Logo" 
              className="w-8 h-8 mr-3"
            />
            <h1 className="text-xl font-bold bg-gradient-to-r from-golden-600 to-primary-600 bg-clip-text text-transparent">BusinessCardHub</h1>
          </div>
          
          <button
            onClick={onSettingsClick}
            className="flex items-center px-4 py-2 text-neutral-600 hover:text-golden-600 hover:bg-golden-50 rounded-lg transition-all duration-200"
          >
            <Settings className="w-5 h-5 mr-2" />
            <span className="font-medium">Settings</span>
          </button>
        </div>
      </div>
    </header>
  );
}