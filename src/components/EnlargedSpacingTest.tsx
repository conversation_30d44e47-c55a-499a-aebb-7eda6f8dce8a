import React, { useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

export default function EnlargedSpacingTest() {
  const [content, setContent] = useState(`
    <h1>Main Heading with Enlarged Spacing</h1>
    <p>This paragraph demonstrates the enlarged spacing between elements. Notice the increased margins and padding throughout the content.</p>
    
    <h2>Secondary Heading</h2>
    <p>Another paragraph showing the enhanced spacing. The gaps between elements are now more generous and provide better visual breathing room.</p>
    
    <h3>Lists with Enhanced Spacing</h3>
    <ul>
      <li>First bullet point with enlarged spacing</li>
      <li>Second bullet point</li>
      <li class="ql-indent-1">Indented bullet with enlarged margins</li>
      <li class="ql-indent-2">Double indented bullet</li>
      <li>Back to normal bullet</li>
    </ul>
    
    <h4>Numbered Lists</h4>
    <ol>
      <li>First numbered item</li>
      <li>Second numbered item</li>
      <li class="ql-indent-1">Indented numbered item</li>
      <li class="ql-indent-2">Double indented numbered item</li>
    </ol>
    
    <blockquote>
      This blockquote now has enlarged padding and margins, making it stand out more prominently from the surrounding content.
    </blockquote>
    
    <p>Here are some indented paragraphs:</p>
    <p class="ql-indent-1">This paragraph is indented level 1 with enlarged spacing</p>
    <p class="ql-indent-2">This paragraph is indented level 2 with enlarged spacing</p>
    <p class="ql-indent-3">This paragraph is indented level 3 with enlarged spacing</p>
    
    <pre>
    This code block also has
    enlarged margins and padding
    for better visual separation
    </pre>
    
    <p>Final paragraph to demonstrate the overall improved spacing and visual hierarchy.</p>
  `);

  // Enhanced configuration with organized toolbar groups (dividers)
  const quillModules = {
    toolbar: [
      [{ 'header': [1, 2, 3, 4, false] }], // Headers group
      ['bold', 'italic', 'underline', 'strike'], // Text formatting group
      [{ 'color': [] }, { 'background': [] }], // Color group
      [{ 'list': 'ordered'}, { 'list': 'bullet' }], // Lists group
      [{ 'indent': '-1'}, { 'indent': '+1' }], // Indentation group
      [{ 'align': [] }], // Alignment group
      ['link', 'image'], // Media group
      ['blockquote', 'code-block'], // Special formatting group
      ['clean'] // Clean group
    ],
    clipboard: {
      matchVisual: false,
    }
  };

  const quillFormats = [
    'header', 'bold', 'italic', 'underline', 'strike',
    'color', 'background', 'list', 'bullet', 'ordered',
    'align', 'link', 'image', 'blockquote', 'code-block', 'indent'
  ];

  const loadSpacingDemo = () => {
    setContent(`
      <h1>Enlarged Spacing Demonstration</h1>
      <p>This content showcases the enlarged spacing between all elements for better visual hierarchy and readability.</p>
      
      <h2>Typography with Enhanced Spacing</h2>
      <p>Notice how each element has more breathing room. Paragraphs have 1.5rem bottom margin instead of 1rem.</p>
      
      <h3>Headings Have Larger Margins</h3>
      <ul>
        <li>H1: 3rem top, 1.5rem bottom (was 2rem top, 1rem bottom)</li>
        <li>H2: 2.5rem top, 1.25rem bottom (was 1.75rem top, 0.875rem bottom)</li>
        <li>H3: 2rem top, 1rem bottom (was 1.5rem top, 0.75rem bottom)</li>
        <li>H4: 1.75rem top, 0.875rem bottom (was 1.25rem top, 0.625rem bottom)</li>
      </ul>
      
      <h4>Lists Have Enhanced Spacing</h4>
      <ol>
        <li>List items now have 0.75rem bottom margin (was 0.5rem)</li>
        <li>Lists themselves have 1.5rem top/bottom margins (was 1rem)</li>
        <li class="ql-indent-1">Indented items have 1rem top/bottom margins (was 0.75rem)</li>
        <li class="ql-indent-2">All indent levels have consistent enlarged spacing</li>
      </ol>
      
      <blockquote>
        Blockquotes now have 2rem top/bottom margins and 1.5rem/2rem padding for a more prominent appearance.
      </blockquote>
      
      <h3>Code Blocks</h3>
      <p>Code blocks also have enlarged spacing:</p>
      
      <pre>
// Code blocks now have:
// - 2rem top/bottom margins (was 1.5rem)
// - 1.5rem padding (was 1rem)
function example() {
  return "Better spacing!";
}
      </pre>
      
      <h3>Indentation Examples</h3>
      <p>Normal paragraph</p>
      <p class="ql-indent-1">Indented paragraph level 1 - now with 1rem top/bottom margins</p>
      <p class="ql-indent-2">Indented paragraph level 2 - consistent enlarged spacing</p>
      <p class="ql-indent-3">Indented paragraph level 3 - all levels enhanced</p>
      
      <p>The overall result is a more spacious, readable, and visually appealing layout!</p>
    `);
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Enlarged Spacing & Toolbar Dividers</h1>
        <p className="text-gray-600 mb-4">
          Test the enlarged spacing for all elements and the new organized toolbar with dividers.
        </p>
        <div className="flex gap-4 mb-4">
          <button
            onClick={loadSpacingDemo}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Load Spacing Demo
          </button>
          <button
            onClick={() => setContent('')}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Clear
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Editor */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Editor with Organized Toolbar</h2>
          <p className="text-sm text-gray-600 mb-4">
            Notice the toolbar now has visual dividers between different tool groups.
          </p>
          <div className="border border-gray-300 rounded-lg overflow-hidden">
            <ReactQuill
              theme="snow"
              value={content}
              onChange={setContent}
              modules={quillModules}
              formats={quillFormats}
              placeholder="Start typing to see the enlarged spacing..."
              style={{ minHeight: '500px' }}
            />
          </div>
          
          {/* Toolbar Analysis */}
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-800 mb-2">Toolbar Groups:</h4>
            <div className="text-sm text-blue-700 space-y-1">
              <div>📝 Headers | 🎨 Text Format | 🌈 Colors</div>
              <div>📋 Lists | ↔️ Indent | ⚖️ Align</div>
              <div>🔗 Media | 📄 Special | 🧹 Clean</div>
            </div>
          </div>
        </div>

        {/* Landing Page Preview */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Landing Page with Enlarged Spacing</h2>
          <p className="text-sm text-gray-600 mb-4">
            See how the enlarged spacing improves readability and visual hierarchy.
          </p>
          <div className="border border-gray-300 rounded-lg p-6 bg-white max-h-[600px] overflow-y-auto">
            <div 
              className="prose prose-sm max-w-none landing-content"
              dangerouslySetInnerHTML={{ __html: content }}
            />
          </div>
        </div>
      </div>

      {/* Spacing Comparison */}
      <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-green-50 p-4 rounded-lg">
          <h3 className="font-semibold text-green-800 mb-2">✅ New Enlarged Spacing</h3>
          <ul className="text-green-700 text-sm space-y-1">
            <li>• H1: 3rem top, 1.5rem bottom</li>
            <li>• H2: 2.5rem top, 1.25rem bottom</li>
            <li>• H3: 2rem top, 1rem bottom</li>
            <li>• H4: 1.75rem top, 0.875rem bottom</li>
            <li>• Paragraphs: 1.5rem bottom</li>
            <li>• Lists: 1.5rem top/bottom, 0.75rem between items</li>
            <li>• Blockquotes: 2rem top/bottom, 1.5rem/2rem padding</li>
            <li>• Code blocks: 2rem top/bottom, 1.5rem padding</li>
            <li>• Indents: 1rem top/bottom for all levels</li>
          </ul>
        </div>

        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="font-semibold text-blue-800 mb-2">🎨 Toolbar Improvements</h3>
          <ul className="text-blue-700 text-sm space-y-1">
            <li>• Visual dividers between tool groups</li>
            <li>• Organized by functionality</li>
            <li>• Better spacing and padding</li>
            <li>• Cleaner, more professional appearance</li>
            <li>• Easier to find related tools</li>
          </ul>
        </div>
      </div>

      {/* Benefits */}
      <div className="mt-6 bg-purple-50 p-4 rounded-lg">
        <h3 className="font-semibold text-purple-800 mb-2">🚀 Benefits of Enlarged Spacing</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-purple-700 text-sm">
          <div>
            <strong>Better Readability:</strong>
            <p>More white space makes content easier to scan and read</p>
          </div>
          <div>
            <strong>Visual Hierarchy:</strong>
            <p>Clear separation between different content sections</p>
          </div>
          <div>
            <strong>Professional Look:</strong>
            <p>More spacious layout appears more polished and modern</p>
          </div>
        </div>
      </div>
    </div>
  );
}
