import React, { useState } from 'react';
import { Plus, X, Linkedin, Twitter, Instagram, Facebook, Youtube, Github, Twitch, BookText as TikTok, <PERSON>nail as <PERSON>nap<PERSON>t, Pointer as Pinterest, Disc as Discord } from 'lucide-react';

interface SocialLink {
  id: string;
  platform: string;
  url: string;
  icon: string;
}

interface SocialLinksEditorProps {
  socialLinks: SocialLink[];
  onSocialLinksChange: (socialLinks: SocialLink[]) => void;
}

const socialPlatforms = [
  { id: 'linkedin', name: 'LinkedIn', icon: Linkedin, placeholder: 'https://linkedin.com/in/username' },
  { id: 'twitter', name: 'Twitter', icon: Twitter, placeholder: 'https://twitter.com/username' },
  { id: 'instagram', name: 'Instagram', icon: Instagram, placeholder: 'https://instagram.com/username' },
  { id: 'facebook', name: 'Facebook', icon: Facebook, placeholder: 'https://facebook.com/username' },
  { id: 'youtube', name: 'YouTube', icon: Youtube, placeholder: 'https://youtube.com/@username' },
  { id: 'github', name: '<PERSON>it<PERSON><PERSON>', icon: Github, placeholder: 'https://github.com/username' },
  { id: 'twitch', name: 'Twitch', icon: Twitch, placeholder: 'https://twitch.tv/username' },
  { id: 'tiktok', name: 'TikTok', icon: TikTok, placeholder: 'https://tiktok.com/@username' },
  { id: 'snapchat', name: 'Snapchat', icon: Snapchat, placeholder: 'https://snapchat.com/add/username' },
  { id: 'pinterest', name: 'Pinterest', icon: Pinterest, placeholder: 'https://pinterest.com/username' },
  { id: 'discord', name: 'Discord', icon: Discord, placeholder: 'https://discord.gg/username' },
];

export default function SocialLinksEditor({ socialLinks, onSocialLinksChange }: SocialLinksEditorProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState('');
  const [newUrl, setNewUrl] = useState('');

  const handleAddLink = () => {
    if (!selectedPlatform || !newUrl) return;

    const platform = socialPlatforms.find(p => p.id === selectedPlatform);
    if (!platform) return;

    const newLink: SocialLink = {
      id: Date.now().toString(),
      platform: platform.name,
      url: newUrl,
      icon: selectedPlatform
    };

    onSocialLinksChange([...socialLinks, newLink]);
    setSelectedPlatform('');
    setNewUrl('');
    setShowAddForm(false);
  };

  const handleRemoveLink = (linkId: string) => {
    onSocialLinksChange(socialLinks.filter(link => link.id !== linkId));
  };

  const handleUpdateLink = (linkId: string, newUrl: string) => {
    onSocialLinksChange(
      socialLinks.map(link => 
        link.id === linkId ? { ...link, url: newUrl } : link
      )
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Social Media Links</h3>
        <button
          onClick={() => setShowAddForm(true)}
          className="flex items-center text-blue-600 hover:text-blue-700 transition-colors text-sm font-medium"
        >
          <Plus className="w-4 h-4 mr-1" />
          Add Social Link
        </button>
      </div>

      {/* Existing Social Links */}
      <div className="space-y-3">
        {socialLinks.map((link) => {
          const platform = socialPlatforms.find(p => p.id === link.icon);
          const IconComponent = platform?.icon;

          return (
            <div key={link.id} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
              <div className="flex items-center space-x-2 min-w-0 flex-1">
                {IconComponent && (
                  <IconComponent className="w-5 h-5 text-gray-600 flex-shrink-0" />
                )}
                <span className="text-sm font-medium text-gray-700 flex-shrink-0">
                  {link.platform}:
                </span>
                <input
                  type="url"
                  value={link.url}
                  onChange={(e) => handleUpdateLink(link.id, e.target.value)}
                  className="flex-1 text-sm border-0 focus:ring-0 p-0 bg-transparent"
                  placeholder={platform?.placeholder}
                />
              </div>
              <button
                onClick={() => handleRemoveLink(link.id)}
                className="text-red-500 hover:text-red-700 transition-colors flex-shrink-0"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          );
        })}
      </div>

      {/* Add New Link Form */}
      {showAddForm && (
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Add New Social Link</h4>
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Platform
              </label>
              <select
                value={selectedPlatform}
                onChange={(e) => setSelectedPlatform(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Select a platform</option>
                {socialPlatforms
                  .filter(platform => !socialLinks.some(link => link.icon === platform.id))
                  .map(platform => (
                    <option key={platform.id} value={platform.id}>
                      {platform.name}
                    </option>
                  ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                URL
              </label>
              <input
                type="url"
                value={newUrl}
                onChange={(e) => setNewUrl(e.target.value)}
                placeholder={
                  selectedPlatform 
                    ? socialPlatforms.find(p => p.id === selectedPlatform)?.placeholder 
                    : 'https://...'
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="flex space-x-2">
              <button
                onClick={handleAddLink}
                disabled={!selectedPlatform || !newUrl}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm"
              >
                Add Link
              </button>
              <button
                onClick={() => {
                  setShowAddForm(false);
                  setSelectedPlatform('');
                  setNewUrl('');
                }}
                className="text-gray-600 hover:text-gray-700 transition-colors px-4 py-2 text-sm"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {socialLinks.length === 0 && !showAddForm && (
        <div className="text-center py-6 border-2 border-dashed border-gray-300 rounded-lg">
          <p className="text-gray-500 text-sm mb-2">No social media links added yet</p>
          <button
            onClick={() => setShowAddForm(true)}
            className="text-blue-600 hover:text-blue-700 transition-colors text-sm font-medium"
          >
            Add your first social link
          </button>
        </div>
      )}
    </div>
  );
}