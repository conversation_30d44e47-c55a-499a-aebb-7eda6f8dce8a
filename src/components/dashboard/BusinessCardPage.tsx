import React, { useState, useEffect } from 'react';
import { Edit3, Eye, Save, X, Palette, User, Image as ImageIcon, Wand2 } from 'lucide-react';
import { BusinessCard, PageBackground, Offer } from '../../types';
import { supabase } from '../../lib/supabase';

import ImageUpload from '../ImageUpload';
import CoverImageUpload from '../CoverImageUpload';
import ThemeSelector from '../ThemeSelector';
import DynamicBusinessCard from '../DynamicBusinessCard';
import BusinessCardComponent from '../BusinessCard';
import PageBackgroundSelector from '../PageBackgroundSelector';
import SocialLinksEditor from '../SocialLinksEditor';
import DragDropCardBuilder from '../dragdrop/DragDropCardBuilder';

interface BusinessCardPageProps {
  businessCard: BusinessCard;
  offers: Offer[];
  onBusinessCardUpdate: (card: BusinessCard) => void;
  onOffersUpdate: (offers: Offer[]) => void;
  hasPermission: (permission: string) => boolean;
  onUpgradeClick: () => void;
}

export default function BusinessCardPage({ 
  businessCard, 
  offers,
  onBusinessCardUpdate, 
  onOffersUpdate,
  hasPermission, 
  onUpgradeClick 
}: BusinessCardPageProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editingCard, setEditingCard] = useState<BusinessCard>(businessCard);
  const [activeSection, setActiveSection] = useState<'design' | 'builder'>('design');
  const [saving, setSaving] = useState(false);

  // Sync editingCard with businessCard prop when it changes
  useEffect(() => {
    setEditingCard(businessCard);
  }, [businessCard]);

  const handleSaveCard = async () => {
    setSaving(true);
    try {
      const { error } = await supabase
        .from('business_cards')
        .update({
          name: editingCard.name,
          title: editingCard.title,
          company: editingCard.company,
          bio: editingCard.bio,
          email: editingCard.email,
          phone: editingCard.phone,
          website: editingCard.website,
          username: editingCard.username,
          profile_image: editingCard.profileImage,
          background_image: editingCard.backgroundImage,
          cover_image: editingCard.coverImage,
          social_links: editingCard.socialLinks,
          theme: editingCard.theme,
          page_background: editingCard.pageBackground,
          offers_title: editingCard.offersTitle || 'Special Offers', // Save offers title
          offers_subtitle: editingCard.offersSubtitle || 'Exclusive deals just for you', // Save offers subtitle
          location: editingCard.location || null, // Save location
          join_year: editingCard.joinYear || null, // Save join year
          components: editingCard.components || [], // Save drag-and-drop components
          updated_at: new Date().toISOString()
        })
        .eq('id', businessCard.id);

      if (!error) {
        onBusinessCardUpdate(editingCard);
        setIsEditing(false);
      }
    } catch (error) {
      console.error('Error saving card:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingCard(businessCard);
    setIsEditing(false);
  };

  const handleThemeChange = (themeId: string) => {
    setEditingCard({ ...editingCard, theme: themeId as BusinessCard['theme'] });
  };

  const handleBackgroundImageChange = (imageUrl: string | null) => {
    setEditingCard({ ...editingCard, backgroundImage: imageUrl || undefined });
  };

  const handleCoverImageChange = (imageUrl: string | null) => {
    setEditingCard({ ...editingCard, coverImage: imageUrl || undefined });
  };

  const handlePageBackgroundChange = (background: PageBackground | null) => {
    setEditingCard({ ...editingCard, pageBackground: background || undefined });
  };

  const handleSocialLinksChange = (socialLinks: any[]) => {
    setEditingCard({ ...editingCard, socialLinks });
  };

  // Helper functions for page background styling
  const getPageBackgroundStyle = (pageBackground?: PageBackground) => {
    if (!pageBackground) {
      return 'bg-gradient-to-br from-neutral-50 to-primary-50';
    }

    switch (pageBackground.type) {
      case 'gradient':
        return pageBackground.value;
      case 'image':
        return '';
      case 'pattern':
        return pageBackground.value;
      default:
        return 'bg-gradient-to-br from-neutral-50 to-primary-50';
    }
  };

  const getPageBackgroundImage = (pageBackground?: PageBackground) => {
    if (pageBackground?.type === 'image') {
      return {
        backgroundImage: `url(${pageBackground.value})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      };
    }
    if (pageBackground?.type === 'pattern') {
      return {
        backgroundImage: pageBackground.value,
        backgroundColor: '#f9fafb',
        backgroundSize: '20px 20px', // default, can be improved by storing patternSize
        backgroundRepeat: 'repeat'
      };
    }
    return {};
  };

  const getPageOverlay = (pageBackground?: PageBackground) => {
    if (pageBackground?.type === 'image' && pageBackground.overlay?.enabled) {
      return {
        backgroundColor: pageBackground.overlay.color,
        opacity: pageBackground.overlay.opacity
      };
    }
    return undefined;
  };

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="flex items-center">
          <User className="w-6 h-6 text-primary-500 mr-3" />
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-neutral-900">My Business Card</h1>
            <p className="text-neutral-600 mt-1">Customize your digital business card</p>
          </div>
        </div>
        {!isEditing ? (
          <button
            onClick={() => setIsEditing(true)}
            className="flex items-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 sm:px-6 py-3 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 w-full sm:w-auto justify-center"
          >
            <Edit3 className="w-4 h-4 mr-2" />
            Edit Card
          </button>
        ) : (
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            <button
              onClick={handleSaveCard}
              disabled={saving}
              className="flex items-center bg-gradient-to-r from-accent-500 to-accent-600 text-white px-4 sm:px-6 py-3 rounded-2xl hover:from-accent-600 hover:to-accent-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 disabled:opacity-50 justify-center"
            >
              <Save className="w-4 h-4 mr-2" />
              {saving ? 'Saving...' : 'Save Changes'}
            </button>
            <button
              onClick={handleCancelEdit}
              className="flex items-center text-neutral-600 hover:text-neutral-700 transition-colors font-medium px-4 py-3 justify-center"
            >
              <X className="w-4 h-4 mr-2" />
              Cancel
            </button>
          </div>
        )}
      </div>

      {/* Section Tabs */}
      <div className="flex space-x-1 bg-white/70 backdrop-blur-sm rounded-2xl p-1 border border-white/50">
        <button
          onClick={() => setActiveSection('builder')}
          className={`flex-1 flex items-center justify-center px-4 sm:px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
            activeSection === 'builder'
              ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-colored'
              : 'text-neutral-600 hover:text-neutral-900 hover:bg-white/50'
          }`}
        >
          <Wand2 className="w-4 h-4 mr-2" />
          <span className="hidden sm:inline">Visual Builder</span>
          <span className="sm:hidden">Builder</span>
        </button>

        <button
          onClick={() => setActiveSection('design')}
          className={`flex-1 flex items-center justify-center px-4 sm:px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
            activeSection === 'design'
              ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-colored'
              : 'text-neutral-600 hover:text-neutral-900 hover:bg-white/50'
          }`}
        >
          <Palette className="w-4 h-4 mr-2" />
          <span className="hidden sm:inline">Card Design</span>
          <span className="sm:hidden">Design</span>
        </button>
      </div>

      {/* Content based on active section */}
      {activeSection === 'builder' ? (
        <DragDropCardBuilder
          businessCard={editingCard}
          offers={offers}
          onBusinessCardUpdate={(card) => setEditingCard(card)}
          onOffersUpdate={onOffersUpdate}
          isEditing={isEditing}
          hasPermission={hasPermission}
          onUpgradeClick={onUpgradeClick}
        />
      ) : (
        <div className="grid lg:grid-cols-2 gap-6 lg:gap-8">
          {/* Edit Form */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-6 sm:p-8 border border-white/50">
            <h2 className="text-lg sm:text-xl font-bold text-neutral-900 mb-6 flex items-center">
              <Palette className="w-5 h-5 mr-2 text-primary-500" />
              Design & Themes
            </h2>

          <div className="space-y-6 sm:space-y-8">
            {/* Cover Photo Upload - Now in Design tab */}
            <div className={!isEditing ? 'opacity-50 pointer-events-none' : ''}>
              <CoverImageUpload
                currentImage={editingCard.coverImage}
                onImageChange={handleCoverImageChange}
                canUpload={hasPermission('change_background')}
                onUpgradeClick={onUpgradeClick}
              />
            </div>

            {/* Page Background Selector */}
            <div className={!isEditing ? 'opacity-50 pointer-events-none' : ''}>
              <PageBackgroundSelector
                currentBackground={editingCard.pageBackground}
                onBackgroundChange={handlePageBackgroundChange}
                canChangeBackground={hasPermission('change_background')}
                onUpgradeClick={onUpgradeClick}
              />
            </div>

            {/* Theme Selector */}
            <div className={!isEditing ? 'opacity-50 pointer-events-none' : ''}>
              <ThemeSelector
                currentTheme={editingCard.theme}
                onThemeChange={handleThemeChange}
              />
            </div>

            {/* Background Image Upload */}
            <div className={!isEditing ? 'opacity-50 pointer-events-none' : ''}>
              <label className="block text-sm font-semibold text-neutral-700 mb-3">
                Background Image
              </label>
              <ImageUpload
                currentImage={editingCard.backgroundImage}
                onImageChange={handleBackgroundImageChange}
                className={!isEditing ? 'opacity-50 pointer-events-none' : ''}
              />
            </div>

            {/* Social Links Editor */}
            <div className={!isEditing ? 'opacity-50 pointer-events-none' : ''}>
              <SocialLinksEditor
                socialLinks={editingCard.socialLinks}
                onSocialLinksChange={handleSocialLinksChange}
              />
            </div>
          </div>
        </div>

        {/* Preview */}
        <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-6 sm:p-8 border border-white/50">
          <div className="flex items-center mb-6 sm:mb-8">
            <Eye className="w-6 h-6 text-primary-500 mr-3" />
            <h2 className="text-xl sm:text-2xl font-bold text-neutral-900">Live Preview</h2>
          </div>
          
          {/* Preview Container with Page Background */}
          <div 
            className={`rounded-2xl p-6 sm:p-8 relative overflow-hidden ${getPageBackgroundStyle(editingCard.pageBackground)}`}
            style={getPageBackgroundImage(editingCard.pageBackground)}
          >
            {/* Page overlay for image backgrounds */}
            {getPageOverlay(editingCard.pageBackground) && (
              <div 
                className="absolute inset-0 pointer-events-none"
                style={getPageOverlay(editingCard.pageBackground)}
              />
            )}

            {/* Business Card Preview - Using the actual DynamicBusinessCard component */}
            <div className="relative z-10 max-w-sm mx-auto">
              {editingCard.components && editingCard.components.length > 0 ? (
                <DynamicBusinessCard 
                  card={editingCard}
                  components={editingCard.components}
                  offers={offers}
                  isCompact={true}
                />
              ) : (
                <BusinessCardComponent 
                  card={editingCard} 
                  offers={offers}
                  isCompact={true}
                  offersTitle={editingCard.offersTitle}
                  offersSubtitle={editingCard.offersSubtitle}
                />
              )}
            </div>
          </div>
        </div>
      </div>
      )}
    </div>
  );
}