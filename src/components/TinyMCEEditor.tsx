import React, { useRef } from 'react';
import { Editor } from '@tinymce/tinymce-react';

interface TinyMCEEditorProps {
  value: string;
  onChange: (content: string) => void;
  placeholder?: string;
  height?: number;
  disabled?: boolean;
}

export default function TinyMCEEditor({ 
  value, 
  onChange, 
  placeholder = "Start typing...", 
  height = 300,
  disabled = false 
}: TinyMCEEditorProps) {
  const editorRef = useRef<any>(null);

  const handleEditorChange = (content: string) => {
    onChange(content);
  };

  return (
    <div className="tinymce-editor-wrapper">
      <Editor
        apiKey="no-api-key" // Using no-api-key for development - you can get a free API key from TinyMCE
        onInit={(evt, editor) => editorRef.current = editor}
        value={value}
        onEditorChange={handleEditorChange}
        disabled={disabled}
        init={{
          height: height,
          menubar: false,
          plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount'
          ],
          toolbar: 'undo redo | blocks | ' +
            'bold italic forecolor | alignleft aligncenter ' +
            'alignright alignjustify | bullist numlist outdent indent | ' +
            'removeformat | help',
          content_style: `
            body { 
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; 
              font-size: 16px; 
              line-height: 1.6;
              color: #374151;
            }
            ul { 
              list-style-type: disc !important; 
              padding-left: 1.5rem !important; 
              margin: 1rem 0 !important; 
            }
            ol { 
              list-style-type: decimal !important; 
              padding-left: 1.5rem !important; 
              margin: 1rem 0 !important; 
            }
            li { 
              margin-bottom: 0.25rem !important; 
              padding-left: 0.25rem !important; 
            }
            p { 
              margin-bottom: 1rem; 
              line-height: 1.7; 
            }
            h1, h2, h3, h4, h5, h6 { 
              margin-top: 1.5rem; 
              margin-bottom: 0.75rem; 
              font-weight: 600; 
            }
            h1 { font-size: 1.875rem; }
            h2 { font-size: 1.5rem; }
            h3 { font-size: 1.25rem; }
            blockquote { 
              border-left: 4px solid #e5e7eb; 
              padding-left: 1rem; 
              margin: 1.5rem 0; 
              font-style: italic; 
              color: #6b7280; 
            }
            a { 
              color: #2563eb; 
              text-decoration: underline; 
            }
            a:hover { 
              color: #1d4ed8; 
            }
            img { 
              max-width: 100%; 
              height: auto; 
              border-radius: 0.5rem; 
              margin: 1rem 0; 
            }
          `,
          placeholder: placeholder,
          branding: false,
          promotion: false,
          resize: false,
          statusbar: false,
          elementpath: false,
          setup: (editor) => {
            editor.on('init', () => {
              // Ensure proper list styling on initialization
              editor.getDoc().head.insertAdjacentHTML('beforeend', `
                <style>
                  ul { list-style-type: disc !important; }
                  ol { list-style-type: decimal !important; }
                  li { display: list-item !important; }
                </style>
              `);
            });
          }
        }}
      />
    </div>
  );
}
