import React, { useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

export default function LandingPageBulletTest() {
  const [content, setContent] = useState(`
    <h2>Landing Page Content Test</h2>
    <p>This content should display bullets properly on the landing page preview.</p>
    <ul>
      <li>First bullet point - should show bullet</li>
      <li>Second bullet point - should show bullet</li>
      <li>Third bullet point with longer text to test wrapping - should show bullet</li>
    </ul>
    <ol>
      <li>First numbered item - should show number</li>
      <li>Second numbered item - should show number</li>
      <li>Third numbered item - should show number</li>
    </ol>
    <p>More content after the lists.</p>
  `);

  // Enhanced Quill configuration
  const quillModules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      [{ 'align': [] }],
      ['link', 'image'],
      ['blockquote', 'code-block'],
      ['clean']
    ],
    clipboard: {
      matchVisual: false,
    }
  };

  const quillFormats = [
    'header', 'bold', 'italic', 'underline', 'strike',
    'color', 'background', 'list', 'bullet', 'ordered',
    'align', 'link', 'image', 'blockquote', 'code-block', 'indent'
  ];

  const testContent = `
    <h2>Test Bullets and Numbers</h2>
    <p>This is a test of bullet points and numbered lists:</p>
    <ul>
      <li>Bullet point one</li>
      <li>Bullet point two</li>
      <li>Bullet point three</li>
    </ul>
    <ol>
      <li>Numbered item one</li>
      <li>Numbered item two</li>
      <li>Numbered item three</li>
    </ol>
    <p>End of test content.</p>
  `;

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Landing Page Bullet Test</h1>
        <p className="text-gray-600 mb-4">
          Test if bullets appear correctly in the landing page preview (like in your offer modal)
        </p>
        <button
          onClick={() => setContent(testContent)}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mb-4"
        >
          Load Test Content
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Editor */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-2">Editor</h2>
          <div className="border border-gray-300 rounded-lg overflow-hidden">
            <ReactQuill
              theme="snow"
              value={content}
              onChange={setContent}
              modules={quillModules}
              formats={quillFormats}
              placeholder="Type here to test..."
              style={{ minHeight: '300px' }}
            />
          </div>
        </div>

        {/* Landing Page Preview (like in your offer modal) */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-2">Landing Page Preview</h2>
          <div className="border border-gray-300 rounded-lg p-6 bg-white">
            {/* This simulates how content is displayed in your landing page */}
            <div 
              className="prose prose-sm max-w-none"
              style={{ color: '#000000' }}
              dangerouslySetInnerHTML={{ __html: content }}
            />
          </div>
        </div>
      </div>

      {/* Additional test cases */}
      <div className="mt-8 space-y-6">
        <h3 className="text-lg font-semibold text-gray-900">Additional Test Cases</h3>
        
        {/* Test Case 1: With inline styles */}
        <div>
          <h4 className="font-medium text-gray-700 mb-2">Test 1: With inline styles (like landing page)</h4>
          <div 
            className="border border-gray-200 rounded p-4 bg-gray-50"
            style={{ backgroundColor: '#ffffff', color: '#000000' }}
          >
            <div 
              className="prose prose-sm max-w-none"
              style={{ color: '#000000' }}
              dangerouslySetInnerHTML={{ __html: content }}
            />
          </div>
        </div>

        {/* Test Case 2: With landing-content class */}
        <div>
          <h4 className="font-medium text-gray-700 mb-2">Test 2: With landing-content class</h4>
          <div className="border border-gray-200 rounded p-4 bg-gray-50">
            <div 
              className="landing-content prose prose-sm max-w-none"
              dangerouslySetInnerHTML={{ __html: content }}
            />
          </div>
        </div>

        {/* Test Case 3: Direct HTML without prose class */}
        <div>
          <h4 className="font-medium text-gray-700 mb-2">Test 3: Direct HTML (no prose class)</h4>
          <div className="border border-gray-200 rounded p-4 bg-gray-50">
            <div dangerouslySetInnerHTML={{ __html: content }} />
          </div>
        </div>

        {/* Test Case 4: Manual HTML to verify CSS works */}
        <div>
          <h4 className="font-medium text-gray-700 mb-2">Test 4: Manual HTML (should definitely work)</h4>
          <div className="border border-gray-200 rounded p-4 bg-gray-50">
            <h2>Manual HTML Test</h2>
            <ul>
              <li>Manual bullet 1</li>
              <li>Manual bullet 2</li>
              <li>Manual bullet 3</li>
            </ul>
            <ol>
              <li>Manual number 1</li>
              <li>Manual number 2</li>
              <li>Manual number 3</li>
            </ol>
          </div>
        </div>
      </div>

      {/* Raw HTML Output */}
      <div className="mt-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Raw HTML Output</h3>
        <pre className="bg-gray-100 p-4 rounded-lg text-xs overflow-auto max-h-40">
          {content}
        </pre>
      </div>

      {/* Instructions */}
      <div className="mt-6 bg-blue-50 p-4 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">What to Check:</h3>
        <ul className="text-blue-700 space-y-1 text-sm">
          <li>• Do bullets appear in the "Landing Page Preview" section?</li>
          <li>• Do numbers appear in the numbered lists?</li>
          <li>• Test all 4 test cases to see which ones work</li>
          <li>• The manual HTML test (Test 4) should definitely show bullets</li>
          <li>• If Test 4 works but others don't, we need to adjust the CSS selectors</li>
        </ul>
      </div>
    </div>
  );
}
