import React, { useState } from 'react';
import { Palette, Check, Upload, X, Crown } from 'lucide-react';

interface Theme {
  id: string;
  name: string;
  preview: {
    background: string;
    accent: string;
    text: string;
  };
  gradient: string;
  isPremium: boolean;
}

interface ThemeSelectorProps {
  currentTheme: string;
  currentBackgroundImage?: string;
  onThemeChange: (themeId: string) => void;
  onBackgroundImageChange: (imageUrl: string | null) => void;
  canChangeBackground: boolean;
  onUpgradeClick?: () => void;
  disabled?: boolean;
}

const themes: Theme[] = [
  {
    id: 'default',
    name: 'Ocean Blue',
    preview: { background: '#4567b7', accent: '#ffffff', text: '#1f2937' },
    gradient: 'from-blue-600 via-purple-500 to-blue-500',
    isPremium: false
  },
  {
    id: 'sunset',
    name: 'Sunset Orange',
    preview: { background: '#ea580c', accent: '#ffffff', text: '#1f2937' },
    gradient: 'from-orange-600 via-red-500 to-pink-500',
    isPremium: false
  },
  {
    id: 'forest',
    name: '<PERSON> Green',
    preview: { background: '#059669', accent: '#ffffff', text: '#1f2937' },
    gradient: 'from-green-600 via-emerald-500 to-teal-500',
    isPremium: false
  },
  {
    id: 'royal',
    name: 'Royal Purple',
    preview: { background: '#7c3aed', accent: '#ffffff', text: '#1f2937' },
    gradient: 'from-purple-600 via-violet-500 to-indigo-500',
    isPremium: true
  },
  {
    id: 'midnight',
    name: 'Midnight Black',
    preview: { background: '#1f2937', accent: '#ffffff', text: '#f9fafb' },
    gradient: 'from-gray-800 via-gray-700 to-black',
    isPremium: true
  },
  {
    id: 'rose',
    name: 'Rose Gold',
    preview: { background: '#be185d', accent: '#ffffff', text: '#1f2937' },
    gradient: 'from-pink-600 via-rose-500 to-red-400',
    isPremium: true
  }
];

const backgroundImages = [
  {
    id: 'abstract-1',
    name: 'Abstract Waves',
    url: 'https://images.pexels.com/photos/1103970/pexels-photo-1103970.jpeg?auto=compress&cs=tinysrgb&w=800',
    isPremium: true
  },
  {
    id: 'geometric-1',
    name: 'Geometric Pattern',
    url: 'https://images.pexels.com/photos/1323712/pexels-photo-1323712.jpeg?auto=compress&cs=tinysrgb&w=800',
    isPremium: true
  },
  {
    id: 'nature-1',
    name: 'Mountain Landscape',
    url: 'https://images.pexels.com/photos/417074/pexels-photo-417074.jpeg?auto=compress&cs=tinysrgb&w=800',
    isPremium: true
  },
  {
    id: 'city-1',
    name: 'City Skyline',
    url: 'https://images.pexels.com/photos/374870/pexels-photo-374870.jpeg?auto=compress&cs=tinysrgb&w=800',
    isPremium: true
  }
];

export default function ThemeSelector({ 
  currentTheme, 
  currentBackgroundImage,
  onThemeChange, 
  onBackgroundImageChange,
  canChangeBackground, 
  onUpgradeClick,
  disabled = false
}: ThemeSelectorProps) {
  const [showBackgroundOptions, setShowBackgroundOptions] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);

  const handleImageUpload = async (file: File) => {
    if (!canChangeBackground) {
      onUpgradeClick?.();
      return;
    }

    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      alert('Image size must be less than 5MB');
      return;
    }

    setUploadingImage(true);

    try {
      // Convert to base64 for demo purposes
      // In production, you'd upload to a service like Supabase Storage
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        onBackgroundImageChange(result);
        setUploadingImage(false);
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Failed to upload image. Please try again.');
      setUploadingImage(false);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      handleImageUpload(files[0]);
    }
  };

  return (
    <div className={`space-y-6 ${disabled ? 'pointer-events-none opacity-50' : ''}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <Palette className="w-5 h-5 mr-2" />
          Card Theme & Background
        </h3>
        {!canChangeBackground && (
          <button
            onClick={onUpgradeClick}
            className="text-sm text-blue-600 hover:text-blue-700 font-medium flex items-center"
          >
            <Crown className="w-4 h-4 mr-1" />
            Upgrade for Premium Themes
          </button>
        )}
      </div>

      {/* Theme Selection */}
      <div>
        <h4 className="text-md font-medium text-gray-800 mb-3">Color Themes</h4>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {themes.map((theme) => {
            const isLocked = theme.isPremium && !canChangeBackground;
            const isSelected = currentTheme === theme.id && !currentBackgroundImage;

            return (
              <div
                key={theme.id}
                className={`relative cursor-pointer rounded-xl overflow-hidden border-2 transition-all duration-200 ${
                  isSelected 
                    ? 'border-blue-500 ring-2 ring-blue-200' 
                    : 'border-gray-200 hover:border-gray-300'
                } ${isLocked ? 'opacity-60' : ''}`}
                onClick={() => {
                  if (disabled) return;
                  if (isLocked) {
                    onUpgradeClick?.();
                  } else {
                    onThemeChange(theme.id);
                    onBackgroundImageChange(null); // Clear background image when selecting theme
                  }
                }}
              >
                {/* Theme Preview */}
                <div className={`h-20 bg-gradient-to-r ${theme.gradient} relative`}>
                  <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                  
                  {/* Mini Profile Preview */}
                  <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2">
                    <div className="w-6 h-6 bg-white rounded-full border-2 border-white shadow-sm"></div>
                  </div>

                  {/* Selected Indicator */}
                  {isSelected && (
                    <div className="absolute top-2 right-2 w-5 h-5 bg-white rounded-full flex items-center justify-center">
                      <Check className="w-3 h-3 text-blue-600" />
                    </div>
                  )}

                  {/* Premium Badge */}
                  {theme.isPremium && (
                    <div className="absolute top-2 left-2">
                      <Crown className="w-4 h-4 text-yellow-400" />
                    </div>
                  )}

                  {/* Lock Indicator */}
                  {isLocked && (
                    <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                      <div className="bg-white rounded-full p-1">
                        <svg className="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                  )}
                </div>

                {/* Theme Info */}
                <div className="p-2 bg-white">
                  <h4 className="font-medium text-gray-900 text-sm">{theme.name}</h4>
                  <div className="flex items-center mt-1 space-x-1">
                    <div 
                      className="w-3 h-3 rounded-full border border-gray-200"
                      style={{ backgroundColor: theme.preview.background }}
                    ></div>
                    <div 
                      className="w-3 h-3 rounded-full border border-gray-200"
                      style={{ backgroundColor: theme.preview.accent }}
                    ></div>
                    <div 
                      className="w-3 h-3 rounded-full border border-gray-200"
                      style={{ backgroundColor: theme.preview.text }}
                    ></div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Background Image Options */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-md font-medium text-gray-800">Background Images</h4>
          <button
            onClick={() => setShowBackgroundOptions(!showBackgroundOptions)}
            className="text-sm text-blue-600 hover:text-blue-700"
          >
            {showBackgroundOptions ? 'Hide Options' : 'Show Options'}
          </button>
        </div>

        {showBackgroundOptions && (
          <div className="space-y-4">
            {/* Current Background Image */}
            {currentBackgroundImage && (
              <div className="relative">
                <div className="text-sm font-medium text-gray-700 mb-2">Current Background</div>
                <div className="relative inline-block">
                  <img 
                    src={currentBackgroundImage} 
                    alt="Current background"
                    className="w-32 h-20 object-cover rounded-lg border-2 border-blue-500"
                  />
                  <button
                    onClick={() => onBackgroundImageChange(null)}
                    className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              </div>
            )}

            {/* Upload Custom Image */}
            <div>
              <div className="text-sm font-medium text-gray-700 mb-2">Upload Custom Background</div>
              <div className="flex items-center space-x-3">
                <label className={`flex items-center px-4 py-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors ${
                  !canChangeBackground ? 'opacity-50 cursor-not-allowed' : ''
                }`}>
                  <Upload className="w-4 h-4 mr-2" />
                  {uploadingImage ? 'Uploading...' : 'Choose Image'}
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileInputChange}
                    className="hidden"
                    disabled={!canChangeBackground || uploadingImage}
                  />
                </label>
                {!canChangeBackground && (
                  <span className="text-xs text-gray-500">Premium feature</span>
                )}
              </div>
            </div>

            {/* Preset Background Images */}
            <div>
              <div className="text-sm font-medium text-gray-700 mb-2">Preset Backgrounds</div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {backgroundImages.map((bg) => {
                  const isLocked = bg.isPremium && !canChangeBackground;
                  const isSelected = currentBackgroundImage === bg.url;

                  return (
                    <div
                      key={bg.id}
                      className={`relative cursor-pointer rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                        isSelected 
                          ? 'border-blue-500 ring-2 ring-blue-200' 
                          : 'border-gray-200 hover:border-gray-300'
                      } ${isLocked ? 'opacity-60' : ''}`}
                      onClick={() => {
                        if (disabled) return;
                        if (isLocked) {
                          onUpgradeClick?.();
                        } else {
                          onBackgroundImageChange(bg.url);
                        }
                      }}
                    >
                      <img 
                        src={bg.url} 
                        alt={bg.name}
                        className="w-full h-16 object-cover"
                      />
                      
                      {/* Selected Indicator */}
                      {isSelected && (
                        <div className="absolute top-1 right-1 w-4 h-4 bg-white rounded-full flex items-center justify-center">
                          <Check className="w-3 h-3 text-blue-600" />
                        </div>
                      )}

                      {/* Premium Badge */}
                      {bg.isPremium && (
                        <div className="absolute top-1 left-1">
                          <Crown className="w-3 h-3 text-yellow-400" />
                        </div>
                      )}

                      {/* Lock Indicator */}
                      {isLocked && (
                        <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                          <div className="bg-white rounded-full p-1">
                            <svg className="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                        </div>
                      )}

                      <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 text-center">
                        {bg.name}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </div>

      {!canChangeBackground && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <p className="text-sm text-blue-800">
            <strong>Unlock Premium Themes & Backgrounds:</strong> Upgrade to Unlimited or higher to access premium themes, custom background images, and advanced customization options.
          </p>
        </div>
      )}
    </div>
  );
}