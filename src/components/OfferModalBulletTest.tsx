import React, { useState } from 'react';

export default function OfferModalBulletTest() {
  const [content, setContent] = useState(`
    <h2>Special Offer Content</h2>
    <p>This is exactly how content appears in your offer modal landing page preview.</p>
    <ul>
      <li>First benefit of this offer</li>
      <li>Second amazing benefit</li>
      <li>Third incredible benefit that's longer to test text wrapping</li>
    </ul>
    <ol>
      <li>Step one to get started</li>
      <li>Step two to continue</li>
      <li>Step three to complete</li>
    </ol>
    <p>Ready to take action? Click the button below!</p>
  `);

  const testContent1 = `
    <h2>Product Features</h2>
    <ul>
      <li>Feature 1: Advanced analytics</li>
      <li>Feature 2: Real-time reporting</li>
      <li>Feature 3: Custom dashboards</li>
      <li>Feature 4: Mobile app included</li>
    </ul>
    <h3>How It Works</h3>
    <ol>
      <li>Sign up for your account</li>
      <li>Connect your data sources</li>
      <li>Configure your preferences</li>
      <li>Start getting insights</li>
    </ol>
  `;

  const testContent2 = `
    <h2>Why Choose Us?</h2>
    <ul>
      <li><strong>Reliable:</strong> 99.9% uptime guarantee</li>
      <li><strong>Fast:</strong> Lightning-fast performance</li>
      <li><strong>Secure:</strong> Bank-level security</li>
    </ul>
    <h3>Getting Started</h3>
    <ol>
      <li>Choose your plan</li>
      <li>Create your account</li>
      <li>Start your free trial</li>
    </ol>
  `;

  // Simulate the exact landing page data structure from your offer modal
  const landingPageData = {
    title: 'Special Limited Time Offer',
    subtitle: 'Get 50% off our premium plan for the first 3 months',
    content: content,
    backgroundColor: '#ffffff',
    textColor: '#000000',
    ctaText: 'Claim Your Discount',
    ctaButtonColor: '#2563eb',
    ctaButtonTextColor: '#ffffff'
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Offer Modal Landing Page Bullet Test</h1>
        <p className="text-gray-600 mb-4">
          This exactly replicates how content is displayed in your offer modal landing page preview.
        </p>
        <div className="flex gap-4 mb-4">
          <button
            onClick={() => setContent(testContent1)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Test Content 1
          </button>
          <button
            onClick={() => setContent(testContent2)}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            Test Content 2
          </button>
          <button
            onClick={() => setContent('')}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Clear
          </button>
        </div>
      </div>

      {/* This is EXACTLY how your offer modal renders the landing page */}
      <div className="border border-gray-300 rounded-lg overflow-hidden">
        <div className="p-4 bg-gray-100 border-b">
          <h3 className="font-semibold text-gray-900">Landing Page Preview (Exact Replica)</h3>
          <p className="text-sm text-gray-600">This uses the same classes and structure as your OfferModal</p>
        </div>
        
        <div 
          className="rounded-lg p-4 sm:p-6 min-h-[400px]"
          style={{ 
            backgroundColor: landingPageData.backgroundColor,
            color: landingPageData.textColor 
          }}
        >
          <h1 className="text-xl sm:text-2xl font-bold mb-2">
            {landingPageData.title}
          </h1>
          <p className="text-base sm:text-lg mb-4 opacity-90">
            {landingPageData.subtitle}
          </p>

          {/* Rich Content - EXACT same structure as OfferModal */}
          <div 
            className="mb-6 prose prose-sm max-w-none landing-content"
            style={{ color: landingPageData.textColor }}
            dangerouslySetInnerHTML={{ 
              __html: content || '<p>Your rich content will appear here...</p>' 
            }}
          />

          <button
            className="px-6 py-3 rounded-lg font-medium transition-colors"
            style={{
              backgroundColor: landingPageData.ctaButtonColor,
              color: landingPageData.ctaButtonTextColor
            }}
          >
            {landingPageData.ctaText}
          </button>
        </div>
      </div>

      {/* Additional test with different background */}
      <div className="mt-8 border border-gray-300 rounded-lg overflow-hidden">
        <div className="p-4 bg-gray-100 border-b">
          <h3 className="font-semibold text-gray-900">Dark Background Test</h3>
        </div>
        
        <div 
          className="rounded-lg p-4 sm:p-6 min-h-[300px]"
          style={{ 
            backgroundColor: '#1f2937',
            color: '#ffffff' 
          }}
        >
          <h1 className="text-xl sm:text-2xl font-bold mb-2">
            Dark Theme Landing Page
          </h1>
          <p className="text-base sm:text-lg mb-4 opacity-90">
            Testing bullets on dark background
          </p>

          <div 
            className="mb-6 prose prose-sm max-w-none landing-content"
            style={{ color: '#ffffff' }}
            dangerouslySetInnerHTML={{ 
              __html: content || '<p>Your rich content will appear here...</p>' 
            }}
          />

          <button
            className="px-6 py-3 rounded-lg font-medium transition-colors bg-blue-600 text-white"
          >
            Get Started
          </button>
        </div>
      </div>

      {/* Raw HTML for debugging */}
      <div className="mt-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Current HTML Content</h3>
        <pre className="bg-gray-100 p-4 rounded-lg text-xs overflow-auto max-h-40">
          {content || 'No content'}
        </pre>
      </div>

      {/* Status check */}
      <div className="mt-6 bg-yellow-50 p-4 rounded-lg">
        <h3 className="font-semibold text-yellow-800 mb-2">What to Check:</h3>
        <ul className="text-yellow-700 space-y-1 text-sm">
          <li>✅ Do you see bullet points (•) before each list item?</li>
          <li>✅ Do you see numbers (1., 2., 3.) before numbered list items?</li>
          <li>✅ Do bullets appear on both light and dark backgrounds?</li>
          <li>✅ Is the text properly indented with bullets visible?</li>
        </ul>
        <p className="mt-2 text-yellow-800 font-medium">
          If you see bullets here, they should also appear in your actual offer modal!
        </p>
      </div>
    </div>
  );
}
