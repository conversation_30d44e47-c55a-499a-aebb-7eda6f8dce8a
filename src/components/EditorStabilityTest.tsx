import React, { useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

export default function EditorStabilityTest() {
  const [content1, setContent1] = useState(`
    <h2>Test Content</h2>
    <p>This content should NOT disappear when you click in the editor.</p>
    <ul>
      <li>First bullet point</li>
      <li>Second bullet point</li>
      <li>Third bullet point</li>
    </ul>
    <ol>
      <li>First numbered item</li>
      <li>Second numbered item</li>
      <li>Third numbered item</li>
    </ol>
    <p>Click anywhere in this editor - content should remain stable!</p>
  `);

  const [content2, setContent2] = useState(`
    <h2>Safe Editor Test</h2>
    <p>This is the safe version with minimal configuration.</p>
    <ul>
      <li>Safe bullet 1</li>
      <li>Safe bullet 2</li>
      <li>Safe bullet 3</li>
    </ul>
    <p>Test clicking, typing, and using toolbar buttons.</p>
  `);

  // Original configuration (potentially problematic)
  const originalModules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      [{ 'align': [] }],
      ['link', 'image'],
      ['blockquote', 'code-block'],
      ['clean']
    ],
    clipboard: {
      matchVisual: false,
    }
  };

  const originalFormats = [
    'header', 'bold', 'italic', 'underline', 'strike',
    'color', 'background', 'list', 'bullet', 'ordered',
    'align', 'link', 'image', 'blockquote', 'code-block', 'indent'
  ];

  // Safe configuration (minimal and stable)
  const safeModules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      ['link'],
      ['clean']
    ]
  };

  const safeFormats = [
    'header', 'bold', 'italic', 'underline',
    'list', 'bullet', 'ordered', 'indent', 'link'
  ];

  const loadTestContent = () => {
    const testContent = `
      <h2>Stability Test Content</h2>
      <p>This content was just loaded. Try clicking in the editor.</p>
      <ul>
        <li>Bullet point A</li>
        <li>Bullet point B</li>
        <li>Bullet point C</li>
      </ul>
      <ol>
        <li>Step 1</li>
        <li>Step 2</li>
        <li>Step 3</li>
      </ol>
      <p><strong>Important:</strong> Content should NOT disappear when you click!</p>
    `;
    setContent1(testContent);
    setContent2(testContent);
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Editor Stability Test</h1>
        <p className="text-gray-600 mb-4">
          Test if the editor content disappears when you click in it. The safe version should be stable.
        </p>
        <button
          onClick={loadTestContent}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mb-4"
        >
          Load Test Content
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Original Editor (potentially problematic) */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-2">Original Editor (Fixed)</h2>
          <p className="text-sm text-gray-600 mb-4">
            This is your current editor with the problematic keyboard bindings removed.
          </p>
          <div className="border border-gray-300 rounded-lg overflow-hidden">
            <ReactQuill
              theme="snow"
              value={content1}
              onChange={setContent1}
              modules={originalModules}
              formats={originalFormats}
              placeholder="Click here to test stability..."
              style={{ minHeight: '300px' }}
            />
          </div>
          <div className="mt-2 text-xs text-gray-500">
            <strong>Test:</strong> Click in the editor, type, use toolbar buttons. Content should remain stable.
          </div>
        </div>

        {/* Safe Editor */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-2">Safe Editor (Minimal)</h2>
          <p className="text-sm text-gray-600 mb-4">
            Minimal configuration that's guaranteed to be stable.
          </p>
          <div className="border border-gray-300 rounded-lg overflow-hidden">
            <ReactQuill
              theme="snow"
              value={content2}
              onChange={setContent2}
              modules={safeModules}
              formats={safeFormats}
              placeholder="Click here to test stability..."
              style={{ minHeight: '300px' }}
            />
          </div>
          <div className="mt-2 text-xs text-gray-500">
            <strong>Test:</strong> This version should be completely stable with no content loss.
          </div>
        </div>
      </div>

      {/* Landing Page Preview Test */}
      <div className="mt-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Landing Page Preview (Bullets Should Work)</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Original Editor Content Preview</h4>
            <div className="border border-gray-200 rounded p-4 bg-white">
              <div 
                className="prose prose-sm max-w-none landing-content"
                dangerouslySetInnerHTML={{ __html: content1 }}
              />
            </div>
          </div>
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Safe Editor Content Preview</h4>
            <div className="border border-gray-200 rounded p-4 bg-white">
              <div 
                className="prose prose-sm max-w-none landing-content"
                dangerouslySetInnerHTML={{ __html: content2 }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Status Check */}
      <div className="mt-6 bg-green-50 p-4 rounded-lg">
        <h3 className="font-semibold text-green-800 mb-2">What to Test:</h3>
        <ul className="text-green-700 space-y-1 text-sm">
          <li>✅ Click anywhere in both editors - content should NOT disappear</li>
          <li>✅ Type new content - should work normally</li>
          <li>✅ Use toolbar buttons - should work without clearing content</li>
          <li>✅ Create bullet lists - should work in editor</li>
          <li>✅ Check preview sections - bullets should appear</li>
        </ul>
        <p className="mt-2 text-green-800 font-medium">
          If both editors are stable, your original OfferModal should now work properly!
        </p>
      </div>

      {/* Error Reporting */}
      <div className="mt-4 bg-red-50 p-4 rounded-lg">
        <h3 className="font-semibold text-red-800 mb-2">If Content Still Disappears:</h3>
        <ul className="text-red-700 space-y-1 text-sm">
          <li>• Use the SafeOfferModal.tsx component instead</li>
          <li>• The safe version has minimal configuration and guaranteed stability</li>
          <li>• Bullets will still work on the landing page preview</li>
        </ul>
      </div>
    </div>
  );
}
