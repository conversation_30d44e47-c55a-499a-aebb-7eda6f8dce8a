import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { 
  SortableContext, 
  verticalListSortingStrategy,
  useSortable 
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { GripVertical, X, Settings, Plus, Edit3, ChevronUp, ChevronDown } from 'lucide-react';
import { CardComponent } from './types';
import { BusinessCard } from '../../types';
import ComponentPreview from './ComponentPreview';

interface CardDropZoneProps {
  components: CardComponent[];
  businessCard: BusinessCard;
  offers?: any[];
  onComponentRemove: (componentId: string) => void;
  onComponentEdit?: (component: CardComponent) => void;
  onComponentMoveUp?: (componentId: string) => void;
  onComponentMoveDown?: (componentId: string) => void;
  highlightedComponent?: string | null;
  hasPermission: (permission: string) => boolean;
}

// Dedicated drop zone for adding new components
function AddComponentDropZone() {
  const { setNodeRef, isOver } = useDroppable({
    id: 'add-component-drop-zone',
  });

  return (
    <div
      ref={setNodeRef}
      className={`mt-6 p-8 border-2 border-dashed rounded-xl transition-all duration-300 ${
        isOver 
          ? 'border-primary-400 bg-primary-50 scale-105 shadow-lg' 
          : 'border-gray-300 bg-gray-50 hover:border-primary-300 hover:bg-primary-25'
      }`}
    >
      <div className="flex flex-col items-center justify-center text-center">
        <div className={`w-16 h-16 rounded-full flex items-center justify-center mb-4 transition-all duration-300 ${
          isOver 
            ? 'bg-primary-100 scale-110' 
            : 'bg-gray-100'
        }`}>
          <Plus className={`w-8 h-8 transition-all duration-300 ${
            isOver 
              ? 'text-primary-600 scale-110' 
              : 'text-gray-400'
          }`} />
        </div>
        <h3 className={`text-lg font-semibold mb-2 transition-all duration-300 ${
          isOver 
            ? 'text-primary-700' 
            : 'text-gray-700'
        }`}>
          {isOver ? '✨ Drop Component Here!' : 'Add Component Here'}
        </h3>
        <p className={`text-sm transition-all duration-300 ${
          isOver 
            ? 'text-primary-600' 
            : 'text-gray-500'
        }`}>
          {isOver 
            ? 'Release to add this component to your card' 
            : 'Drag components from the palette to add them to your card'
          }
        </p>
      </div>
    </div>
  );
}

function SortableComponent({ 
  component, 
  businessCard, 
  offers,
  onRemove, 
  onEdit,
  onMoveUp,
  onMoveDown,
  isHighlighted,
  isFirst,
  isLast,
  isLocked,
  hasPermission
}: { 
  component: CardComponent; 
  businessCard: BusinessCard;
  offers?: any[];
  onRemove: (id: string) => void;
  onEdit?: (component: CardComponent) => void;
  onMoveUp?: (id: string) => void;
  onMoveDown?: (id: string) => void;
  isHighlighted?: boolean;
  isFirst?: boolean;
  isLast?: boolean;
  isLocked?: boolean;
  hasPermission: (permission: string) => boolean;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: component.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`relative bg-white rounded-xl border-2 transition-all duration-200 ${
        isDragging ? 'opacity-50 shadow-lg scale-105' : ''
      } ${
        isHighlighted 
          ? 'border-green-400 shadow-lg scale-105 bg-green-50' 
          : 'border-gray-100 hover:border-primary-200'
      }`}
    >
      {/* Component Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-100 bg-gray-50 rounded-t-xl">
        <div className="flex items-center space-x-2">
          <div
            {...attributes}
            {...listeners}
            className="cursor-grab active:cursor-grabbing p-2 hover:bg-gray-200 rounded transition-colors duration-200"
          >
            <GripVertical className="w-4 h-4 text-gray-400" />
          </div>
          <span className="text-sm font-medium text-gray-700">{component.title}</span>
        </div>
        <div className="flex items-center space-x-1">
          {/* Move Up Button - Only show for non-locked components */}
          {onMoveUp && !isFirst && !isLocked && (
            <button
              onClick={() => onMoveUp(component.id)}
              className="p-1 hover:bg-blue-100 rounded transition-colors duration-200"
              title="Move Up"
            >
              <ChevronUp className="w-4 h-4 text-blue-500" />
            </button>
          )}
          
          {/* Move Down Button - Only show for non-locked components */}
          {onMoveDown && !isLast && !isLocked && (
            <button
              onClick={() => {
                console.log(`🔽 DOWN BUTTON CLICKED for: ${component.title} (${component.id})`);
                onMoveDown(component.id);
              }}
              className="p-1 hover:bg-blue-100 rounded transition-colors duration-200"
              title="Move Down"
            >
              <ChevronDown className="w-4 h-4 text-blue-500" />
            </button>
          )}
          
          {/* Show locked indicator for locked components */}
          {isLocked && (
            <span className="text-xs text-gray-500 px-2 py-1 bg-gray-100 rounded">
              🔒 Locked
            </span>
          )}
          
          {onEdit && (
            <button
              onClick={() => onEdit(component)}
              className="p-1 hover:bg-gray-200 rounded transition-colors duration-200"
              title="Edit Component"
            >
              <Settings className="w-4 h-4 text-gray-400" />
            </button>
          )}
          {/* Remove Button - Only show for non-locked components */}
          {!isLocked && (
            <button
              onClick={() => onRemove(component.id)}
              className="p-1 hover:bg-red-100 rounded transition-colors duration-200"
              title="Remove Component"
            >
              <X className="w-4 h-4 text-red-400" />
            </button>
          )}
        </div>
      </div>
      
      {/* Component Content */}
      <div className="px-4">
        <ComponentPreview 
          component={component} 
          businessCard={businessCard}
          offers={offers}
        />
      </div>
    </div>
  );
}

export default function CardDropZone({ 
  components, 
  businessCard, 
  offers,
  onComponentRemove,
  onComponentEdit,
  onComponentMoveUp,
  onComponentMoveDown,
  highlightedComponent,
  hasPermission
}: CardDropZoneProps) {
  // Check if a component is locked (can't be moved/reordered)
  const isComponentLocked = (componentId: string): boolean => {
    const component = components.find(c => c.id === componentId);
    if (!component) return false;
    
    // Lock core components by type (profile, bio, social)
    return ['profile', 'bio', 'social'].includes(component.type);
  };
  const { setNodeRef, isOver } = useDroppable({
    id: 'card-drop-zone',
  });

  // Sort components by their current order
  const sortedComponents = [...components].sort((a, b) => a.order - b.order);
  console.log('Sorted components:', sortedComponents.map((c, i) => `${i}: ${c.title} (order: ${c.order}, id: ${c.id})`));

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-soft border border-white/50 p-6" data-card-template>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <div className="w-5 h-5 text-primary-500 mr-3">📄</div>
          <h2 className="text-lg font-bold text-gray-900">Card Template</h2>
        </div>
        
        {/* Component Count and Limits */}
        <div className="text-sm text-gray-600">
          {!hasPermission('unlimited_offers') && (
            <>
              <span className="inline-flex items-center px-2 py-1 rounded-full bg-amber-100 text-amber-800">
                Offers: {sortedComponents.filter(c => c.type === 'offers').length}/3
              </span>
              <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                Links: {sortedComponents.filter(c => c.type === 'link').length}/3
              </span>
              <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full bg-purple-100 text-purple-800">
                Custom: {sortedComponents.filter(c => c.type === 'text').length}/2
              </span>
            </>
          )}
          <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full bg-gray-100 text-gray-700">
            Total: {sortedComponents.length}
          </span>
        </div>
      </div>
      
      <div
        ref={setNodeRef}
        className={`min-h-[400px] rounded-xl border-2 border-dashed transition-all duration-200 ${
          isOver 
            ? 'border-primary-400 bg-primary-50 shadow-lg scale-[1.02]' 
            : 'border-gray-300 bg-gray-50'
        }`}
      >
        {sortedComponents.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full py-12">
            <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-4">
              <span className="text-2xl">📄</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-700 mb-2">Empty Card</h3>
            <p className="text-gray-500 text-center max-w-xs">
              Drag components from the palette to build your business card
            </p>
          </div>
        ) : (
          <div className="p-4">
            <SortableContext items={sortedComponents.map(c => c.id)} strategy={verticalListSortingStrategy}>
              <div className="space-y-3">
                {sortedComponents.map((component, index) => {
                  const isFirst = index === 0;
                  const isLast = index === sortedComponents.length - 1;
                  console.log(`Component ${component.title}: index=${index}, isFirst=${isFirst}, isLast=${isLast}, total=${sortedComponents.length}`);
                  
                  return (
                    <SortableComponent
                      key={component.id}
                      component={component}
                      businessCard={businessCard}
                      offers={offers}
                      onRemove={onComponentRemove}
                      onEdit={onComponentEdit}
                      onMoveUp={onComponentMoveUp}
                      onMoveDown={onComponentMoveDown}
                      isHighlighted={highlightedComponent === component.id}
                      isFirst={isFirst}
                      isLast={isLast}
                      isLocked={isComponentLocked(component.id)}
                      hasPermission={hasPermission}
                    />
                  );
                })}
              </div>
            </SortableContext>
            
            {/* Dedicated "Add Component Here" Drop Zone */}
            <AddComponentDropZone />
          </div>
        )}
      </div>
      
      {isOver && (
        <div className="mt-4 p-3 bg-primary-100 rounded-lg animate-pulse">
          <p className="text-sm text-primary-700 text-center font-medium">
            ✨ Drop component here to add it to your card
          </p>
        </div>
      )}
    </div>
  );
}
