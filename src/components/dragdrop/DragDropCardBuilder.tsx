import React, { useState, useEffect, useCallback } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  PointerSensor,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
  closestCenter,
  closestCorners,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { 
  Eye, 
  Save, 
  RotateCcw, 
  Layout,
  Download,
  Upload,
  Sparkles,
  User,
  ChevronUp,
  ChevronDown,
} from 'lucide-react';
import { BusinessCard, Offer } from '../../types';
import { CardComponent } from './types';
import ComponentPalette from './ComponentPalette';
import CardDropZone from './CardDropZone';
import ComponentEditor from './ComponentEditor';
import ContentEditor from './ContentEditor';
import SidebarEditor from './SidebarEditor';

interface DragDropCardBuilderProps {
  businessCard: BusinessCard;
  offers: Offer[];
  onBusinessCardUpdate: (card: BusinessCard) => void;
  onOffersUpdate: (offers: Offer[]) => void;
  isEditing: boolean;
  hasPermission: (permission: string) => boolean;
  onUpgradeClick?: () => void;
}

const defaultComponents: CardComponent[] = [
  {
    id: 'default-profile',
    type: 'profile',
    title: 'Profile Section',
    description: 'Name, title, company, and avatar',
    icon: 'User',
    isActive: true,
    order: 0,
    config: {
      showAvatar: true,
      showName: true,
      showTitle: true,
      showCompany: true
    }
  },
  {
    id: 'default-bio',
    type: 'bio',
    title: 'Bio Section',
    description: 'Personal description and story',
    icon: 'FileText',
    isActive: true,
    order: 1,
    config: {
      maxLength: 200,
      showReadMore: true
    }
  },
  {
    id: 'default-social',
    type: 'social',
    title: 'Social Links',
    description: 'Social media profiles and contact info',
    icon: 'Share2',
    isActive: true,
    order: 2,
    config: {
      showIcons: true,
      maxLinks: 4,
      showContactInfo: true
    }
  }
];

export default function DragDropCardBuilder({
  businessCard,
  offers,
  onBusinessCardUpdate,
  onOffersUpdate,
  isEditing,
  hasPermission,
  onUpgradeClick
}: DragDropCardBuilderProps) {
  const [components, setComponents] = useState<CardComponent[]>(defaultComponents);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'builder' | 'preview'>('builder');
  const [showSuccessMessage, setShowSuccessMessage] = useState<string | null>(null);
  const [highlightedComponent, setHighlightedComponent] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [editingComponent, setEditingComponent] = useState<CardComponent | null>(null);
  const [showContentEditor, setShowContentEditor] = useState(false);
  const [showSidebarEditor, setShowSidebarEditor] = useState(false);
  const [sidebarComponent, setSidebarComponent] = useState<CardComponent | null>(null);

  // Load components from business card when it changes
  useEffect(() => {
    console.log('Loading components:', { 
      savedComponents: businessCard.components, 
      willUseDefault: !businessCard.components || businessCard.components.length === 0 
    });
    
    if (businessCard.components && businessCard.components.length > 0) {
      setComponents(businessCard.components);
    } else {
      // If no components saved, use default components
      setComponents(defaultComponents);
    }
  }, [businessCard.components]);

  // Check if a component is locked (can't be moved/reordered)
  const isComponentLocked = (componentId: string): boolean => {
    const component = components.find(c => c.id === componentId);
    if (!component) return false;
    
    // Lock core components by type (profile, bio, social)
    return ['profile', 'bio', 'social'].includes(component.type);
  };

  // Auto-save components whenever they change
  const saveComponentsRef = useCallback((componentsToSave: CardComponent[]) => {
    if (componentsToSave.length > 0) {
      try {
        const updatedCard = { ...businessCard, components: componentsToSave };
        onBusinessCardUpdate(updatedCard);
      } catch (error) {
        console.error('Error auto-saving components:', error);
      }
    }
  }, [businessCard, onBusinessCardUpdate]);

  useEffect(() => {
    // Only auto-save if we have components and they're different from the business card's components
    const hasChanges = JSON.stringify(components) !== JSON.stringify(businessCard.components || []);
    if (hasChanges && components.length > 0) {
      // Debounce the save to avoid too many API calls
      const timeoutId = setTimeout(() => saveComponentsRef(components), 1000);
      return () => clearTimeout(timeoutId);
    }
  }, [components, businessCard.components, saveComponentsRef]);

  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 10,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250,
        tolerance: 5,
      },
    })
  );

  const handleDragStart = (event: DragStartEvent) => {
    const componentId = event.active.id as string;
    
    // Prevent locked components from being dragged
    if (isComponentLocked(componentId)) {
      console.log(`🔒 Component ${componentId} is locked and cannot be dragged`);
      return;
    }
    
    console.log('Drag start:', componentId);
    setActiveId(componentId);
  };

  const handleDragOver = (event: DragOverEvent) => {
    const { active, over } = event;
    
    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;

    console.log('Drag over:', { activeId, overId, hasActiveInComponents: !!components.find(c => c.id === activeId), hasOverInComponents: !!components.find(c => c.id === overId) });

    // Only handle reordering if both items are in the components list
    if (activeId !== overId && components.find(c => c.id === activeId) && components.find(c => c.id === overId)) {
      console.log('Reordering components...');
      setComponents((items) => {
        const oldIndex = items.findIndex((item) => item.id === activeId);
        const newIndex = items.findIndex((item) => item.id === overId);

        console.log('Moving from index', oldIndex, 'to index', newIndex);

        const newItems = arrayMove(items, oldIndex, newIndex);
        
        // Update the order property for all components during drag
        return newItems.map((item, index) => ({
          ...item,
          order: index
        }));
      });
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    console.log('Drag end:', { activeId: active.id, overId: over?.id });
    setActiveId(null);

    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;

    // Handle dropping from palette to add component drop zone
    if (over.id === 'add-component-drop-zone') {
      const draggedComponent = active.data.current as CardComponent;
      
      if (draggedComponent) {
        const newComponent: CardComponent = {
          ...draggedComponent,
          id: `${draggedComponent.type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          order: components.length
        };
        
        setComponents(prev => [...prev, newComponent]);
        setShowSuccessMessage(`Added ${draggedComponent.title}! (Auto-saved)`);
        setTimeout(() => setShowSuccessMessage(null), 2000);
        
        // Highlight the new component
        setHighlightedComponent(newComponent.id);
        setTimeout(() => setHighlightedComponent(null), 3000);
      }
      return;
    }

    // Handle component reordering within the card
    if (activeId !== overId && components.find(c => c.id === activeId) && components.find(c => c.id === overId)) {
      setComponents((items) => {
        const oldIndex = items.findIndex((item) => item.id === activeId);
        const newIndex = items.findIndex((item) => item.id === overId);

        const newItems = arrayMove(items, oldIndex, newIndex);
        
        // Update the order property for all components
        return newItems.map((item, index) => ({
          ...item,
          order: index
        }));
      });
      
      // Show feedback that reordering was successful
      setShowSuccessMessage('Component order updated! (Auto-saved)');
      setTimeout(() => setShowSuccessMessage(null), 2000);
    }
  };

  const handleComponentRemove = (componentId: string) => {
    // Prevent locked components from being removed
    if (isComponentLocked(componentId)) {
      console.log(`🔒 Component ${componentId} is locked and cannot be removed`);
      return;
    }
    
    setComponents(prev => prev.filter(c => c.id !== componentId));
    setShowSuccessMessage('Component removed! (Auto-saved)');
    setTimeout(() => setShowSuccessMessage(null), 2000);
  };

  const handleComponentEdit = (component: CardComponent) => {
    setEditingComponent(component);
  };

  const handleComponentConfigSave = (updatedComponent: CardComponent) => {
    setComponents(prev => 
      prev.map(comp => 
        comp.id === updatedComponent.id ? updatedComponent : comp
      )
    );
    setEditingComponent(null);
    setShowSuccessMessage('Component updated successfully!');
    setTimeout(() => setShowSuccessMessage(null), 2000);
  };

  const handleSidebarSave = (updatedCard: BusinessCard, updatedComponent: CardComponent) => {
    // Update component first
    const updatedComponents = components.map(comp => 
      comp.id === updatedComponent.id ? updatedComponent : comp
    );
    
    // Update components state
    setComponents(updatedComponents);
    
    // Update business card with new components
    const cardWithUpdatedComponents = {
      ...updatedCard,
      components: updatedComponents
    };
    onBusinessCardUpdate(cardWithUpdatedComponents);
    
    setShowSidebarEditor(false);
    setSidebarComponent(null);
    setShowSuccessMessage('Component updated successfully!');
    setTimeout(() => setShowSuccessMessage(null), 2000);
  };

  const handleContentSave = (updatedCard: BusinessCard) => {
    onBusinessCardUpdate(updatedCard);
    setShowContentEditor(false);
    setShowSuccessMessage('Card content updated successfully!');
    setTimeout(() => setShowSuccessMessage(null), 2000);
  };

  const handleComponentAdd = (component: CardComponent) => {
    console.log('🔍 Adding component:', component.type, component.title);
    
    // Check if user can add more components based on their plan
    if (component.type === 'offers' && !hasPermission('unlimited_offers')) {
      // For offers, check if user has reached their limit
      const currentOfferComponents = components.filter(c => c.type === 'offers').length;
      if (currentOfferComponents >= 3) {
        setShowSuccessMessage('❌ You can only add 3 offers on the free plan. Upgrade for unlimited offers!');
        setTimeout(() => setShowSuccessMessage(null), 4000);
        if (onUpgradeClick) {
          onUpgradeClick();
        }
        return;
      }
    }
    
    // Check link component limit for free users
    if (component.type === 'link' && !hasPermission('unlimited_offers')) {
      const currentLinkComponents = components.filter(c => c.type === 'link').length;
      if (currentLinkComponents >= 3) {
        setShowSuccessMessage('❌ You can only add 3 links on the free plan. Upgrade for unlimited links!');
        setTimeout(() => setShowSuccessMessage(null), 4000);
        if (onUpgradeClick) {
          onUpgradeClick();
        }
        return;
      }
    }
    
    // Check custom text component limit for free users
    if (component.type === 'text' && !hasPermission('unlimited_offers')) {
      const currentTextComponents = components.filter(c => c.type === 'text').length;
      if (currentTextComponents >= 2) {
        setShowSuccessMessage('❌ You can only add 2 custom text components on the free plan. Upgrade for unlimited customization!');
        setTimeout(() => setShowSuccessMessage(null), 4000);
        if (onUpgradeClick) {
          onUpgradeClick();
        }
        return;
      }
    }
    
    const newComponent: CardComponent = {
      ...component,
      id: `${component.type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      order: components.length,
      // Create a completely independent config object
      config: component.config ? JSON.parse(JSON.stringify(component.config)) : undefined
    };
    
    setComponents(prev => [...prev, newComponent]);
    setShowSuccessMessage(`Added ${component.title}! (Auto-saved)`);
    setTimeout(() => setShowSuccessMessage(null), 2000);
    
    // Highlight the new component
    setHighlightedComponent(newComponent.id);
    setTimeout(() => setHighlightedComponent(null), 3000);
    
    // Auto-scroll to the card template to show the new component
    setTimeout(() => {
      const cardTemplate = document.querySelector('[data-card-template]');
      if (cardTemplate) {
        cardTemplate.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }, 100);
  };

  const handleReset = () => {
    if (!hasPermission('change_background')) {
      setShowSuccessMessage('❌ Layout reset is a premium feature. Upgrade to unlock!');
      setTimeout(() => setShowSuccessMessage(null), 3000);
      if (onUpgradeClick) {
        onUpgradeClick();
      }
      return;
    }
    
    setComponents(defaultComponents);
    setShowSuccessMessage('Reset to default layout!');
    setTimeout(() => setShowSuccessMessage(null), 2000);
  };

  const handleMoveUp = (componentId: string) => {
    // Prevent locked components from moving
    if (isComponentLocked(componentId)) {
      console.log(`🔒 Component ${componentId} is locked and cannot be moved`);
      return;
    }

    setComponents(prev => {
      // Sort components first, just like CardDropZone does
      const sortedComponents = [...prev].sort((a, b) => a.order - b.order);
      const currentIndex = sortedComponents.findIndex(c => c.id === componentId);
      
      if (currentIndex <= 0) return prev; // Already at top or not found
      
      // Swap with the previous component in sorted array
      [sortedComponents[currentIndex], sortedComponents[currentIndex - 1]] = [sortedComponents[currentIndex - 1], sortedComponents[currentIndex]];
      
      // Update order property for all components
      return sortedComponents.map((component, index) => ({
        ...component,
        order: index
      }));
    });
  };

  const handleMoveDown = (componentId: string) => {
    // Prevent locked components from moving
    if (isComponentLocked(componentId)) {
      console.log(`🔒 Component ${componentId} is locked and cannot be moved`);
      return;
    }

    setComponents(prev => {
      console.log(`\n=== MOVE DOWN DEBUG ===`);
      console.log(`Trying to move: ${componentId}`);
      
      // Sort components first, just like CardDropZone does
      const sortedComponents = [...prev].sort((a, b) => a.order - b.order);
      console.log(`Sorted components:`, sortedComponents.map((c, i) => `${i}: ${c.title} (id: ${c.order})`));
      
      const currentIndex = sortedComponents.findIndex(c => c.id === componentId);
      console.log(`Found component at SORTED index: ${currentIndex}`);
      
      if (currentIndex >= sortedComponents.length - 1 || currentIndex === -1) {
        console.log(`❌ Cannot move down: currentIndex=${currentIndex}, total=${sortedComponents.length}`);
        return prev; // Already at bottom or not found
      }
      
      console.log(`✅ Can move! Swapping SORTED index ${currentIndex} with ${currentIndex + 1}`);
      console.log(`Before swap: "${sortedComponents[currentIndex].title}" <-> "${sortedComponents[currentIndex + 1].title}"`);
      
      // Swap with the next component in sorted array
      [sortedComponents[currentIndex], sortedComponents[currentIndex + 1]] = [sortedComponents[currentIndex + 1], sortedComponents[currentIndex]];
      
      console.log(`After swap: "${sortedComponents[currentIndex].title}" <-> "${sortedComponents[currentIndex + 1].title}"`);
      
      // Update order property for all components
      const result = sortedComponents.map((component, index) => ({
        ...component,
        order: index
      }));
      
      console.log('Final order:', result.map(c => `${c.title}(${c.order})`));
      console.log(`=== END MOVE DOWN DEBUG ===\n`);
      return result;
    });
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Convert components to business card data
      const updatedCard = { ...businessCard };
      
      // Update business card with component data
      components.forEach(component => {
        switch (component.type) {
          case 'profile':
            // Profile data is already in businessCard
            break;
          case 'bio':
            // Bio data is already in businessCard
            break;
          case 'contact':
            // Contact data is already in businessCard
            break;
          case 'social':
            // Social data is already in businessCard
            break;
          case 'offers':
            updatedCard.offersTitle = component.config.title || 'Special Offers';
            updatedCard.offersSubtitle = component.config.subtitle || 'Exclusive deals just for you';
            break;
          case 'location':
            // Location data is already in businessCard
            break;
        }
      });
      
      // Save components to business card
      updatedCard.components = components;
      
      // Call the parent's update function to save to database
      onBusinessCardUpdate(updatedCard);
      
      setShowSuccessMessage('Card layout saved successfully!');
      setTimeout(() => setShowSuccessMessage(null), 2000);
    } catch (error) {
      console.error('Error saving:', error);
      setShowSuccessMessage('Save failed. Please try again.');
      setTimeout(() => setShowSuccessMessage(null), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  const handleExport = () => {
    if (!hasPermission('change_background')) {
      setShowSuccessMessage('❌ Export is a premium feature. Upgrade to unlock!');
      setTimeout(() => setShowSuccessMessage(null), 3000);
      if (onUpgradeClick) {
        onUpgradeClick();
      }
      return;
    }
    
    const dataStr = JSON.stringify(components, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `business-card-layout-${businessCard.username || 'export'}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    setShowSuccessMessage('Layout exported successfully!');
    setTimeout(() => setShowSuccessMessage(null), 2000);
  };

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!hasPermission('change_background')) {
      setShowSuccessMessage('❌ Import is a premium feature. Upgrade to unlock!');
      setTimeout(() => setShowSuccessMessage(null), 3000);
      if (onUpgradeClick) {
        onUpgradeClick();
      }
      return;
    }
    
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const importedComponents = JSON.parse(content);
        
        if (Array.isArray(importedComponents)) {
          setComponents(importedComponents);
          setShowSuccessMessage('Layout imported successfully!');
          setTimeout(() => setShowSuccessMessage(null), 2000);
        } else {
          throw new Error('Invalid file format');
        }
      } catch (error) {
        setShowSuccessMessage('Import failed: Invalid file format');
        setTimeout(() => setShowSuccessMessage(null), 3000);
      }
    };
    reader.readAsText(file);
    
    // Reset the input
    event.target.value = '';
  };



  if (!isEditing) {
    return (
      <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-6 border border-white/50">
        <div className="flex items-center mb-4">
          <Layout className="w-5 h-5 text-primary-500 mr-2" />
          <h2 className="text-lg font-bold text-neutral-900">Visual Builder</h2>
        </div>
        <div className="text-center text-gray-500 py-8">
          <Sparkles className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p className="text-sm font-medium text-gray-600 mb-2">Visual Builder Ready!</p>
          <p className="text-sm text-gray-500 mb-4">Click "Edit Card" in the header to start building your card</p>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-sm mx-auto">
            <p className="text-xs text-blue-700">
              💡 <strong>Tip:</strong> The Visual Builder lets you drag and drop components to create your perfect business card layout!
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div className="flex items-center">
          <Layout className="w-8 h-8 text-primary-500 mr-4" />
          <div>
            <h1 className="text-3xl lg:text-4xl font-bold text-neutral-900">Visual Builder</h1>
            <p className="text-neutral-600 mt-2 text-lg">Build your business card by dragging components</p>
            
            {/* Permission Status */}
            <div className="mt-3 flex items-center gap-3 text-sm">
              {!hasPermission('unlimited_offers') && (
                <>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                    ⚠️ 3 offers max
                  </span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    🔗 3 links max
                  </span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    ✏️ 2 custom max
                  </span>
                </>
              )}
              {!hasPermission('change_background') && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  🔒 Basic features only
                </span>
              )}
              {hasPermission('change_background') && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  ✨ Premium features unlocked
                </span>
              )}
            </div>
          </div>
        </div>
        
        <div className="flex flex-col xl:flex-row gap-4 xl:gap-6">
          {/* Primary Actions */}
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => setViewMode(viewMode === 'builder' ? 'preview' : 'builder')}
              className="flex items-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 py-3 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105"
            >
              <Eye className="w-5 h-5 mr-2" />
              {viewMode === 'builder' ? 'Preview' : 'Builder'}
            </button>
            
            <button
              onClick={() => setShowContentEditor(true)}
              className="flex items-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-3 rounded-2xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105"
            >
              <User className="w-5 h-5 mr-2" />
              Edit Content
            </button>
          </div>

          {/* Secondary Actions */}
          {viewMode === 'builder' && (
            <div className="flex flex-wrap items-center gap-4 xl:gap-6">
              <div className="flex items-center gap-4">
                <button
                  onClick={handleReset}
                  className="flex items-center text-neutral-600 hover:text-neutral-700 transition-colors font-medium px-3 py-2 rounded-lg hover:bg-gray-100"
                >
                  <RotateCcw className="w-4 h-4 mr-2" />
                  Reset
                </button>
                
                {/* Export/Import - Premium features */}
                {hasPermission('change_background') ? (
                  <>
                    <button
                      onClick={handleExport}
                      className="flex items-center text-neutral-600 hover:text-neutral-700 transition-colors font-medium px-3 py-2 rounded-lg hover:bg-gray-100"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Export
                    </button>
                    <label className="flex items-center text-neutral-600 hover:text-neutral-700 transition-colors font-medium px-3 py-2 rounded-lg hover:bg-gray-100 cursor-pointer">
                      <Upload className="w-4 h-4 mr-2" />
                      Import
                      <input
                        type="file"
                        accept=".json"
                        onChange={handleImport}
                        className="hidden"
                      />
                    </label>
                  </>
                ) : (
                  <div className="flex items-center gap-2">
                    <button
                      onClick={onUpgradeClick}
                      className="flex items-center text-neutral-500 hover:text-neutral-600 transition-colors font-medium px-3 py-2 rounded-lg hover:bg-gray-100"
                      title="Upgrade to unlock export/import"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Export
                      <span className="ml-1 text-xs bg-amber-100 text-amber-700 px-1.5 py-0.5 rounded">PRO</span>
                    </button>
                    <button
                      onClick={onUpgradeClick}
                      className="flex items-center text-neutral-500 hover:text-neutral-600 transition-colors font-medium px-3 py-2 rounded-lg hover:bg-gray-100"
                      title="Upgrade to unlock export/import"
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      Import
                      <span className="ml-1 text-xs bg-amber-100 text-amber-700 px-1.5 py-0.5 rounded">PRO</span>
                    </button>
                  </div>
                )}
              </div>
              
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="flex items-center bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-3 rounded-2xl hover:from-green-600 hover:to-green-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Save className={`w-5 h-5 mr-2 ${isSaving ? 'animate-spin' : ''}`} />
                {isSaving ? 'Saving...' : 'Save Layout'}
              </button>
            </div>
          )}
        </div>
      </div>

      {viewMode === 'builder' ? (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCorners}
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDragEnd={handleDragEnd}
        >
          <div className="grid xl:grid-cols-2 gap-8 lg:gap-12">
            {/* Component Palette */}
            <div className="order-2 lg:order-1">
              <ComponentPalette 
                onComponentAdd={handleComponentAdd} 
                hasPermission={hasPermission}
                onUpgradeClick={onUpgradeClick}
              />
            </div>

            {/* Card Drop Zone */}
            <div className="order-1 lg:order-2">
              <CardDropZone
                components={components}
                businessCard={businessCard}
                offers={offers}
                onComponentRemove={handleComponentRemove}
                onComponentEdit={handleComponentEdit}
                onComponentMoveUp={handleMoveUp}
                onComponentMoveDown={handleMoveDown}
                highlightedComponent={highlightedComponent}
                hasPermission={hasPermission}
              />
            </div>
          </div>
        </DndContext>
      ) : (
        /* Preview Mode */
        <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-6 sm:p-8 border border-white/50">
          <div className="flex items-center mb-6">
            <Eye className="w-6 h-6 text-primary-500 mr-3" />
            <h2 className="text-xl sm:text-2xl font-bold text-neutral-900">Card Preview</h2>
          </div>
          
          <div className="max-w-sm mx-auto">
            <div className="bg-white rounded-2xl shadow-large overflow-hidden border border-white/50">
              {/* Cover Photo or Theme Background */}
              {businessCard.coverImage ? (
                <div className="h-24 relative overflow-hidden">
                  <img 
                    src={businessCard.coverImage} 
                    alt="Cover"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                </div>
              ) : (
                <div className="h-24 relative overflow-hidden">
                  <div className="w-full h-full bg-gradient-to-r from-blue-600 via-purple-500 to-blue-500"></div>
                  <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                </div>
              )}

              {/* Components */}
              <div className="px-6 pb-6">
                {components.map((component) => (
                  <div key={component.id} className="mb-4 last:mb-0">
                    {component.type === 'profile' && (
                      <div className="text-center">
                        <div className="w-16 h-16 rounded-full overflow-hidden border-4 border-white shadow-medium bg-white mx-auto mb-4 -mt-8">
                          <img 
                            src={component.config?.profileImage || businessCard.profileImage} 
                            alt={businessCard.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <h3 className="text-lg font-bold text-gray-900">{businessCard.name}</h3>
                        <p className="text-gray-600">{businessCard.title}</p>
                        <p className="text-gray-500 text-sm">{businessCard.company}</p>
                      </div>
                    )}
                    
                    {component.type === 'bio' && (
                      <p className="text-gray-700 text-center leading-relaxed">
                        {businessCard.bio}
                      </p>
                    )}
                    
                    {component.type === 'contact' && (
                      <div className="space-y-2 text-center">
                        <div className="text-sm text-gray-600">{businessCard.email}</div>
                        {businessCard.phone !== '+****************' && (
                          <div className="text-sm text-gray-600">{businessCard.phone}</div>
                        )}
                        {businessCard.website !== 'https://yourwebsite.com' && (
                          <div className="text-sm text-gray-600">{businessCard.website}</div>
                        )}
                      </div>
                    )}
                    
                    {component.type === 'social' && (
                      <div className="flex justify-center space-x-3">
                        {businessCard.socialLinks.length > 0 ? (
                          businessCard.socialLinks.slice(0, 4).map((link, index) => (
                            <div key={index} className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                              <span className="text-xs font-bold text-gray-600">{link.platform.charAt(0).toUpperCase()}</span>
                            </div>
                          ))
                        ) : (
                          ['L', 'T', 'I', 'F'].map((letter, index) => (
                            <div key={index} className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                              <span className="text-xs font-bold text-gray-600">{letter}</span>
                            </div>
                          ))
                        )}
                      </div>
                    )}
                    
                    {component.type === 'offers' && (
                      <div className="text-center">
                        <h4 className="font-bold text-gray-800 mb-2">Special Offers</h4>
                        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-3">
                          <p className="text-sm text-gray-700">Add your special offers here</p>
                        </div>
                      </div>
                    )}
                    
                    {component.type === 'location' && (
                      <div className="flex items-center justify-center space-x-4 text-sm text-gray-600">
                        <span>{businessCard.location || 'Remote'}</span>
                        <span>•</span>
                        <span>Joined {businessCard.joinYear || 2020}</span>
                      </div>
                    )}
                    
                    {component.type === 'text' && (
                      <div className="text-center">
                        <h4 className="text-sm text-gray-600" dangerouslySetInnerHTML={{ __html: component.config?.htmlContent || 'Add your custom text here...' }} />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Success Message */}
      {showSuccessMessage && (
        <div className="fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
          {showSuccessMessage}
        </div>
      )}

      {/* Component Editor */}
      {editingComponent && (
        <ComponentEditor
          component={editingComponent}
          businessCard={businessCard}
          onClose={() => setEditingComponent(null)}
          onSave={handleComponentConfigSave}
          onBusinessCardUpdate={onBusinessCardUpdate}
        />
      )}

      {/* Content Editor */}
      {showContentEditor && (
        <ContentEditor
          businessCard={businessCard}
          onClose={() => setShowContentEditor(false)}
          onSave={handleContentSave}
        />
      )}

      {/* Sidebar Editor */}
      {showSidebarEditor && sidebarComponent && (
        <SidebarEditor
          component={sidebarComponent}
          businessCard={businessCard}
          offers={offers}
          onClose={() => {
            setShowSidebarEditor(false);
            setSidebarComponent(null);
          }}
          onSave={handleContentSave}
          onComponentSave={(updatedComponent) => handleSidebarSave(businessCard, updatedComponent)}
          onOffersUpdate={onOffersUpdate}
        />
      )}
    </div>
  );
}
