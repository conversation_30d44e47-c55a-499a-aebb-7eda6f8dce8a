export interface CardComponent {
  id: string;
  type: 'profile' | 'bio' | 'contact' | 'social' | 'offers' | 'link' | 'location' | 'custom' | 'text' | 'divider';
  title: string;
  description: string;
  icon: string;
  isActive: boolean;
  order: number;
  config?: {
    // Profile config
    showAvatar?: boolean;
    showName?: boolean;
    showTitle?: boolean;
    showCompany?: boolean;
    profileImage?: string;
    
    // Bio config
    maxLength?: number;
    showReadMore?: boolean;
    
    // Contact config
    showEmail?: boolean;
    showPhone?: boolean;
    showWebsite?: boolean;
    
    // Social config
    platforms?: string[];
    layout?: 'grid' | 'horizontal';
    
    // Offers config
    title?: string;
    subtitle?: string;
    maxOffers?: number;
    offerTitle?: string;
    offerDescription?: string;
    offerButtonText?: string;
    offerUrl?: string;
    landingPageHeaderImage?: string;
    landingPageTitle?: string;
    landingPageSubtitle?: string;
    landingPageContent?: string;
    landingPageCtaText?: string;
    landingPageCtaUrl?: string;
    landingPageBgColor?: string;
    landingPageTextColor?: string;
    landingPageCtaButtonColor?: string;
    landingPageCtaTextColor?: string;
    
    // Business card data fields (for editing in visual builder)
    name?: string;
    jobTitle?: string;
    company?: string;
    bio?: string;
    email?: string;
    phone?: string;
    website?: string;
    location?: string;
    joinYear?: number;
    socialLinks?: any;
    
    // Location config
    showLocation?: boolean;
    showJoinYear?: boolean;
    
    // Link config
    url?: string;
    description?: string;
    
    // Custom config
    content?: string;
    backgroundColor?: string;
    textColor?: string;
    borderRadius?: number;
    padding?: number;
    alignment?: string;
    width?: string;
    customWidth?: number;
    marginBottom?: number;
    
    // Custom Text/HTML widget config
    htmlContent?: string;
    showHtmlEditor?: boolean;
    customCss?: string;
    allowHtml?: boolean;
    
    // Divider config
    dividerStyle?: 'solid' | 'dashed' | 'dotted' | 'double' | 'gradient';
    dividerColor?: string;
    dividerThickness?: number;
    dividerWidth?: number; // percentage
    dividerMargin?: number;
  };
}

export interface ComponentLayout {
  version: string;
  defaultMode: 'dragdrop' | 'form';
  lastUsedMode: 'dragdrop' | 'form';
}