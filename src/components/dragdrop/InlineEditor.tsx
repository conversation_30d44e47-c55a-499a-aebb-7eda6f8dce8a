import React, { useState, useRef, useEffect } from 'react';
import { Edit3, Save, X } from 'lucide-react';
import { BusinessCard } from '../../types';

interface InlineEditorProps {
  businessCard: BusinessCard;
  onSave: (updatedCard: BusinessCard) => void;
  onClose: () => void;
  field: 'name' | 'title' | 'company' | 'bio' | 'email' | 'phone' | 'website' | 'location';
  label: string;
  icon?: React.ComponentType<{ className?: string }>;
  type?: 'text' | 'textarea' | 'email' | 'tel' | 'url';
  placeholder?: string;
  maxLength?: number;
  position?: 'top-right' | 'bottom-right' | 'top-left' | 'bottom-left';
}

export default function InlineEditor({
  businessCard,
  onSave,

  field,
  label,
  icon: Icon,
  type = 'text',
  placeholder,
  maxLength,
  position = 'top-right'
}: InlineEditorProps) {
  const [localValue, setLocalValue] = useState(businessCard[field] ?? '');
  const [isEditing, setIsEditing] = useState(false);
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);
  const lastSavedValue = useRef(businessCard[field] ?? '');

  // Update local value when businessCard prop changes, but only if we're not currently editing
  // and the value has actually changed
  useEffect(() => {
    if (!isEditing && businessCard[field] !== lastSavedValue.current) {
      setLocalValue(businessCard[field] ?? '');
      lastSavedValue.current = businessCard[field] ?? '';
    }
  }, [businessCard[field], isEditing, field]);

  // Focus and select text when editing starts
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  const handleSave = () => {
    // Only save if the value has actually changed
    if (localValue !== lastSavedValue.current) {
      const updatedCard = {
        ...businessCard,
        [field]: localValue
      };
      onSave(updatedCard);
      lastSavedValue.current = localValue;
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setLocalValue(lastSavedValue.current);
    setIsEditing(false);
  };
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setLocalValue(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && type !== 'textarea') {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-right':
        return 'absolute bottom-2 right-2 transform translate-y-1';
      case 'top-left':
        return 'absolute top-2 left-2 transform -translate-x-1';
      case 'bottom-left':
        return 'absolute bottom-2 left-2 transform translate-y-1 -translate-x-1';
      case 'top-right':
      default:
        return 'absolute top-2 right-2 transform -translate-y-1';
    }
  };

  if (!isEditing) {
    return (
      <button
        onClick={() => setIsEditing(true)}
        className="absolute top-2 right-2 p-1.5 bg-white/90 backdrop-blur-sm rounded-lg shadow-md border border-gray-200 hover:bg-white hover:shadow-lg transition-all duration-200 opacity-0 group-hover:opacity-100 z-10"
        title={`Edit ${label}`}
      >
        {Icon ? <Icon className="w-3 h-3 text-gray-600" /> : <Edit3 className="w-3 h-3 text-gray-600" />}
      </button>
    );
  }

  return (
    <div className={`${getPositionClasses()} bg-white rounded-lg shadow-lg border border-gray-200 p-3 min-w-[200px] z-20`}>
      <div className="flex items-center justify-between mb-2">
        <label className="text-xs font-medium text-gray-700 flex items-center">
          {Icon && <Icon className="w-3 h-3 mr-1" />}
          {label}
        </label>
        <div className="flex items-center space-x-2">
          <button
            onClick={handleSave}
            className="p-2 text-green-600 hover:bg-green-50 rounded transition-colors"
            title="Save"
          >
            <Save className="w-4 h-4" />
          </button>
          <button
            onClick={handleCancel}
            className="p-2 text-gray-500 hover:bg-gray-50 rounded transition-colors"
            title="Cancel"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
      
      {type === 'textarea' ? (
        <textarea
          ref={inputRef as React.RefObject<HTMLTextAreaElement>}
          value={localValue}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          maxLength={maxLength}
          rows={3}
          className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
        />
      ) : (
        <input
          ref={inputRef as React.RefObject<HTMLInputElement>}
          type={type}
          value={localValue}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          maxLength={maxLength}
          className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent"
        />
      )}
      
      {maxLength && (
        <div className="text-xs text-gray-500 mt-1 text-right">
          {localValue.length}/{maxLength}
        </div>
      )}
    </div>
  );
}
