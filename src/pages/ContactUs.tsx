import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Zap } from 'lucide-react';
import Footer from '../components/Footer';
import { useAuth } from '../contexts/AuthContext';

export default function ContactUs() {
  const { user } = useAuth();
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className="min-h-screen relative overflow-hidden bg-black">
      {/* Animated Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-pink-900">
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}></div>
        </div>
      </div>

      {/* Floating Particles */}
      <div className="fixed inset-0 pointer-events-none">
        {[...Array(50)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 3}s`
            }}
          />
        ))}
      </div>

      {/* Mouse Trail Effect */}
      <div 
        className="fixed w-4 h-4 bg-gradient-to-r from-cyan-400 to-pink-400 rounded-full pointer-events-none z-50 mix-blend-difference transition-transform duration-100 ease-out"
        style={{
          left: mousePosition.x - 8,
          top: mousePosition.y - 8,
          transform: isHovering ? 'scale(2)' : 'scale(1)'
        }}
      />

      {/* Animated Background Elements */}
      <div className="fixed inset-0 pointer-events-none z-10">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-cyan-400/20 to-pink-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-pink-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-purple-400/20 to-cyan-400/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
      </div>

      {/* Main Navigation Bar (matches HomePage) */}
      <nav className="relative z-40 bg-white/95 backdrop-blur-xl border-b border-white/20 sticky top-0">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to="/" className="flex items-center group">
              <div className="w-10 h-10 bg-gradient-to-br from-cyan-500 to-pink-500 rounded-xl flex items-center justify-center mr-3 shadow-lg group-hover:scale-105 transition-transform duration-200">
                <div className="relative w-6 h-6">
                  {/* B letter with modern design */}
                  <div className="absolute inset-0 bg-white rounded-sm"></div>
                  <div className="absolute top-0 left-0 w-1 h-6 bg-cyan-500 rounded-l-sm"></div>
                  <div className="absolute top-1 left-1 w-4 h-1 bg-cyan-500 rounded-sm"></div>
                  <div className="absolute top-2.5 left-1 w-3 h-1 bg-pink-500 rounded-sm"></div>
                  <div className="absolute bottom-1 left-1 w-4 h-1 bg-pink-500 rounded-sm"></div>
                  <div className="absolute top-1 right-1 w-1 h-4 bg-pink-500 rounded-sm"></div>
                </div>
              </div>
              <span className="text-2xl font-black bg-gradient-to-r from-cyan-600 to-pink-600 bg-clip-text text-transparent tracking-tight">
                BUZZZ
              </span>
            </Link>
            <div>
              {user ? (
                <Link 
                  to="/dashboard" 
                  className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 text-sm"
                >
                  Dashboard
                </Link>
              ) : (
                <Link
                  to="/"
                  className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 text-sm"
                >
                  Sign In
                </Link>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Contact Page Content */}
      <div className="relative z-30 min-h-screen flex flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-2xl w-full bg-white/10 backdrop-blur-xl rounded-2xl shadow-xl p-8 md:p-12 border border-white/20">
          <h1 className="text-3xl font-bold text-white mb-2">Contact Us</h1>
          <p className="text-white/80 mb-8">We'd love to hear from you! Fill out the form below and we'll get back to you soon.</p>
          <form
            action="https://formspree.io/f/mrbkandd"
            method="POST"
            className="space-y-6"
          >
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-white/90 mb-1">Name</label>
              <input
                type="text"
                name="name"
                id="name"
                required
                className="block w-full rounded-lg border border-white/20 bg-white/10 backdrop-blur-xl px-4 py-2 text-white placeholder-white/50 focus:ring-primary-500 focus:border-primary-500 transition"
                placeholder="Your Name"
              />
            </div>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-white/90 mb-1">Email</label>
              <input
                type="email"
                name="email"
                id="email"
                required
                className="block w-full rounded-lg border border-white/20 bg-white/10 backdrop-blur-xl px-4 py-2 text-white placeholder-white/50 focus:ring-primary-500 focus:border-primary-500 transition"
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label htmlFor="message" className="block text-sm font-medium text-white/90 mb-1">Message</label>
              <textarea
                name="message"
                id="message"
                rows={5}
                required
                className="block w-full rounded-lg border border-white/20 bg-white/10 backdrop-blur-xl px-4 py-2 text-white placeholder-white/50 focus:ring-primary-500 focus:border-primary-500 transition"
                placeholder="How can we help you?"
              />
            </div>
            <button
              type="submit"
              className="w-full bg-gradient-to-r from-cyan-400 to-pink-400 text-black font-semibold py-2.5 rounded-lg transition-all duration-200 hover:from-pink-400 hover:to-cyan-400 transform hover:scale-105"
              onMouseEnter={() => setIsHovering(true)}
              onMouseLeave={() => setIsHovering(false)}
            >
              Send Message
            </button>
          </form>
          <div className="mt-10 border-t border-white/20 pt-8">
            <h2 className="text-lg font-semibold text-white mb-2">Contact Information</h2>
            <p className="text-white/80 mb-1">Email: <a href="mailto:<EMAIL>" className="text-cyan-400 hover:underline"><EMAIL></a></p>
            <p className="text-white/80 mb-4">Manong, Perak, Malaysia</p>
            <div className="w-full h-48 rounded-lg overflow-hidden bg-white/10 backdrop-blur-xl border border-white/20 flex items-center justify-center">
              <span className="text-white/50">[Map Placeholder]</span>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="relative z-40">
        <Footer />
      </div>
    </div>
  );
} 