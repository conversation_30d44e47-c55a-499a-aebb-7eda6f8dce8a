import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Zap } from 'lucide-react';
import Footer from '../components/Footer';
import { useAuth } from '../contexts/AuthContext';

export default function TermsOfService() {
  const { user } = useAuth();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className="min-h-screen relative overflow-hidden bg-black">
      {/* Animated Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-pink-900">
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}></div>
        </div>
      </div>

      {/* Floating Particles */}
      <div className="fixed inset-0 pointer-events-none">
        {[...Array(50)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 3}s`
            }}
          />
        ))}
      </div>

      {/* Mouse Trail Effect */}
      <div 
        className="fixed w-4 h-4 bg-gradient-to-r from-cyan-400 to-pink-400 rounded-full pointer-events-none z-50 mix-blend-difference transition-transform duration-100 ease-out"
        style={{
          left: mousePosition.x - 8,
          top: mousePosition.y - 8,
          transform: isHovering ? 'scale(2)' : 'scale(1)'
        }}
      />

      {/* Animated Background Elements */}
      <div className="fixed inset-0 pointer-events-none z-10">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-cyan-400/20 to-pink-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-pink-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-purple-400/20 to-cyan-400/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
      </div>

      <nav className="relative z-40 bg-white/95 backdrop-blur-xl border-b border-white/20 sticky top-0">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to="/" className="flex items-center group">
              <div className="w-10 h-10 bg-gradient-to-br from-cyan-500 to-pink-500 rounded-xl flex items-center justify-center mr-3 shadow-lg group-hover:scale-105 transition-transform duration-200">
                <div className="relative w-6 h-6">
                  {/* B letter with modern design */}
                  <div className="absolute inset-0 bg-white rounded-sm"></div>
                  <div className="absolute top-0 left-0 w-1 h-6 bg-cyan-500 rounded-l-sm"></div>
                  <div className="absolute top-1 left-1 w-4 h-1 bg-cyan-500 rounded-sm"></div>
                  <div className="absolute top-2.5 left-1 w-3 h-1 bg-pink-500 rounded-sm"></div>
                  <div className="absolute bottom-1 left-1 w-4 h-1 bg-pink-500 rounded-sm"></div>
                  <div className="absolute top-1 right-1 w-1 h-4 bg-pink-500 rounded-sm"></div>
                </div>
              </div>
              <span className="text-2xl font-black bg-gradient-to-r from-cyan-600 to-pink-600 bg-clip-text text-transparent tracking-tight">
                BUZZZ
              </span>
            </Link>
            <div>
              {user ? (
                <Link 
                  to="/dashboard" 
                  className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 text-sm"
                >
                  Dashboard
                </Link>
              ) : (
                <Link
                  to="/"
                  className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 text-sm"
                >
                  Sign In
                </Link>
              )}
            </div>
          </div>
        </div>
      </nav>
      <div className="relative z-30 min-h-screen flex flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-2xl w-full bg-white/10 backdrop-blur-xl rounded-2xl shadow-xl p-8 md:p-12 border border-white/20">
          <h1 className="text-3xl font-bold text-white mb-4">Terms of Service</h1>
          <div className="prose prose-invert max-w-none">
            <h2 className="text-white">Terms of Service for Buzzz.my</h2>
            <p className="text-white/80"><strong>Effective Date:</strong> 21 June 2025</p>
            <p className="text-white/80">Welcome to Buzzz.my. By accessing or using our platform, you agree to be bound by these Terms of Service ("Terms"). Please read them carefully.</p>
            <h3 className="text-white">1. Overview</h3>
            <p className="text-white/80">Buzzz.my is a platform that allows users to create and share digital business cards and personalized link pages ("Services"). These Terms apply to all users and visitors of the platform.</p>
            <h3 className="text-white">2. Account Registration</h3>
            <ul className="text-white/80">
              <li>- You must be at least 13 years old to use Buzzz.my.</li>
              <li>- You are responsible for maintaining the confidentiality of your account.</li>
              <li>- You agree to provide accurate and current information when registering or updating your profile.</li>
            </ul>
            <h3 className="text-white">3. User Content</h3>
            <p className="text-white/80">You retain ownership of the content you post (e.g., your profile details, images, and links), but by posting on Buzzz.my, you grant us a non-exclusive license to display, host, and distribute your content for the purpose of operating the service.</p>
            <p className="text-white/80">You agree not to upload or link to:</p>
            <ul className="text-white/80">
              <li>- Inappropriate, offensive, or harmful content</li>
              <li>- Illegal material or copyrighted content without permission</li>
              <li>- Malicious code or spam</li>
            </ul>
            <p className="text-white/80">We reserve the right to remove content or suspend accounts that violate these Terms.</p>
            <h3 className="text-white">4. Public Profiles</h3>
            <p className="text-white/80">By default, your profile is public and shareable via a unique Buzzz.my URL. You are responsible for the content you publish, including personal or contact information.</p>
            <h3 className="text-white">5. Acceptable Use</h3>
            <p className="text-white/80">You agree to use Buzzz.my only for lawful purposes. You will not:</p>
            <ul className="text-white/80">
              <li>- Abuse or exploit the platform</li>
              <li>- Use automated bots or scripts to access the service</li>
              <li>- Attempt to interfere with the platform's security or functionality</li>
            </ul>
            <h3 className="text-white">6. Termination</h3>
            <p className="text-white/80">We may suspend or terminate your account at any time if you violate these Terms or misuse the platform. You may also delete your account at any time.</p>
            <h3 className="text-white">7. Disclaimers</h3>
            <p className="text-white/80">Buzzz.my is provided "as is" without warranties of any kind. We do not guarantee uninterrupted service or that the platform will be error-free.</p>
            <p className="text-white/80">You use Buzzz.my at your own risk.</p>
            <h3 className="text-white">8. Limitation of Liability</h3>
            <p className="text-white/80">To the fullest extent permitted by law, Buzzz.my and its team shall not be liable for any direct, indirect, incidental, or consequential damages arising out of your use of the platform.</p>
            <h3 className="text-white">9. Changes to the Terms</h3>
            <p className="text-white/80">We may update these Terms from time to time. Continued use of the platform after changes means you accept the new Terms. Major changes will be communicated via email or site notification.</p>
            <h3 className="text-white">10. Contact Us</h3>
            <p className="text-white/80">If you have questions about these Terms, contact us at:</p>
            <ul className="text-white/80">
              <li>📧 Email: <a href="mailto:<EMAIL>" className="text-cyan-400 hover:underline"><EMAIL></a></li>
              <li>🌐 Website: <a href="https://buzzz.my" target="_blank" rel="noopener noreferrer" className="text-cyan-400 hover:underline">https://buzzz.my</a></li>
            </ul>
          </div>
        </div>
      </div>
      <div className="relative z-40">
        <Footer />
      </div>
    </div>
  );
} 