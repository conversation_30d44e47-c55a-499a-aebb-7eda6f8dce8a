@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.4s ease-out;
}

.animate-slide-in-left {
  animation: slide-in-left 0.4s ease-out;
}

.animate-scale-in {
  animation: scale-in 0.3s ease-out;
}

.animate-slide-in {
  animation: slide-in 0.3s ease-out;
}

/* Golden Accent Classes */
.golden-gradient {
  background: linear-gradient(135deg, #f5b700 0%, #f8d86b 50%, #f5b700 100%);
}

.golden-glow {
  box-shadow: 0 0 20px rgba(245, 183, 0, 0.3);
}

.golden-border {
  border: 2px solid #f5b700;
}

.golden-text-shadow {
  text-shadow: 0 2px 4px rgba(245, 183, 0, 0.3);
}

.golden-highlight {
  background: linear-gradient(120deg, transparent 0%, rgba(245, 183, 0, 0.1) 50%, transparent 100%);
}

.golden-accent-border {
  border-left: 4px solid #f5b700;
}

.golden-button {
  background: linear-gradient(135deg, #f5b700 0%, #e6a800 100%);
  color: white;
  transition: all 0.3s ease;
}

.golden-button:hover {
  background: linear-gradient(135deg, #e6a800 0%, #c28a00 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 183, 0, 0.4);
}

/* Safe Area Support for Modern Mobile Devices */
@supports (padding: max(0px)) {
  .safe-area-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  
  .safe-area-bottom {
    padding-bottom: max(1.25rem, env(safe-area-inset-bottom));
  }
  
  .safe-area-left {
    padding-left: max(1rem, env(safe-area-inset-left));
  }
  
  .safe-area-right {
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  .mobile-cta-container {
    padding-bottom: max(1.25rem, env(safe-area-inset-bottom));
  }
  
  .mobile-content {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  
  /* Improve touch targets for mobile */
  button, a {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Better sidebar scrolling on mobile */
  .sidebar-mobile-scroll {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  .sidebar-mobile-scroll::-webkit-scrollbar {
    display: none;
  }
  
  /* Prevent body scroll when sidebar is open */
  body.sidebar-open {
    overflow: hidden;
  }
  
  /* Improve mobile navigation touch targets */
  .mobile-nav-item {
    padding: 12px 16px;
    margin: 2px 0;
  }
  
  /* Better mobile spacing */
  .mobile-safe-padding {
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}

/* Quill Editor Customization */
.ql-editor {
  min-height: 200px;
  font-family: inherit;
  line-height: 1.6;
  font-size: 16px; /* Increased from default 14px */
}

.ql-toolbar {
  border-top: 1px solid #e5e7eb;
  border-left: 1px solid #e5e7eb;
  border-right: 1px solid #e5e7eb;
  border-bottom: none;
  background: #f9fafb;
}

.ql-container {
  border-bottom: 1px solid #e5e7eb;
  border-left: 1px solid #e5e7eb;
  border-right: 1px solid #e5e7eb;
  border-top: none;
}

.ql-editor.ql-blank::before {
  color: #9ca3af;
  font-style: normal;
  font-size: 16px; /* Match the editor font size */
}

/* Fix bullet points and lists in Quill - More aggressive approach */
.ql-editor ul,
.ql-container ul,
.ql-snow ul {
  list-style-type: disc !important;
  padding-left: 2rem !important;
  margin: 1rem 0 !important;
  list-style-position: outside !important;
}

.ql-editor ol,
.ql-container ol,
.ql-snow ol {
  list-style-type: decimal !important;
  padding-left: 2rem !important;
  margin: 1rem 0 !important;
  list-style-position: outside !important;
}

.ql-editor li,
.ql-container li,
.ql-snow li {
  display: list-item !important;
  margin-bottom: 0.5rem !important;
  padding-left: 0.5rem !important;
  list-style: inherit !important;
}

.ql-editor ul li,
.ql-container ul li,
.ql-snow ul li {
  list-style-type: disc !important;
}

.ql-editor ol li,
.ql-container ol li,
.ql-snow ol li {
  list-style-type: decimal !important;
}

/* Force list styles even with nested elements */
.ql-editor .ql-indent-1 { margin-left: 3rem !important; }
.ql-editor .ql-indent-2 { margin-left: 4.5rem !important; }
.ql-editor .ql-indent-3 { margin-left: 6rem !important; }
.ql-editor .ql-indent-4 { margin-left: 7.5rem !important; }
.ql-editor .ql-indent-5 { margin-left: 9rem !important; }
.ql-editor .ql-indent-6 { margin-left: 10.5rem !important; }
.ql-editor .ql-indent-7 { margin-left: 12rem !important; }
.ql-editor .ql-indent-8 { margin-left: 13.5rem !important; }

/* Override any conflicting styles */
.ql-editor * {
  list-style: inherit !important;
}

/* Ensure bullets are visible */
.ql-editor ul > li::marker,
.ql-container ul > li::marker {
  content: "• " !important;
  color: currentColor !important;
}

.ql-editor ol > li::marker,
.ql-container ol > li::marker {
  color: currentColor !important;
}

/* Prose styling for landing page content */
.prose {
  max-width: none;
}

.prose h1 {
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.prose h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  margin-top: 1.5rem;
  line-height: 1.3;
}

.prose h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  margin-top: 1.25rem;
  line-height: 1.4;
}

.prose p {
  margin-bottom: 1rem;
  line-height: 1.7;
  font-size: 16px; /* Ensure consistent font size */
}

.prose ul, .prose ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.5rem;
  line-height: 1.6;
  font-size: 16px; /* Consistent font size for list items */
}

.prose blockquote {
  border-left: 4px solid currentColor;
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  opacity: 0.8;
  font-size: 16px; /* Consistent font size */
}

.prose img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1.5rem 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.prose a {
  text-decoration: underline;
  text-underline-offset: 2px;
}

.prose a:hover {
  opacity: 0.8;
}

.prose strong {
  font-weight: 600;
}

.prose em {
  font-style: italic;
}

.prose code {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.prose pre {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

/* Ensure all text in the editor has consistent sizing */
.ql-editor * {
  font-size: inherit !important;
}

/* Override Quill's default font sizes for better consistency */
.ql-editor h1 {
  font-size: 1.875rem !important;
}

.ql-editor h2 {
  font-size: 1.5rem !important;
}

.ql-editor h3 {
  font-size: 1.25rem !important;
}

.ql-editor p, .ql-editor div, .ql-editor span {
  font-size: 16px !important;
}

.ql-editor ul li, .ql-editor ol li {
  font-size: 16px !important;
}

/* NUCLEAR OPTION - Force bullets and numbers to appear */
.ql-editor ul,
.ql-editor ol {
  counter-reset: list-counter;
}

.ql-editor ul li {
  position: relative;
  padding-left: 1.5rem !important;
}

.ql-editor ul li:before {
  content: "•" !important;
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  color: currentColor !important;
  font-weight: bold !important;
  font-size: 1.2em !important;
  line-height: 1.2 !important;
}

.ql-editor ol li {
  position: relative;
  padding-left: 2rem !important;
  counter-increment: list-counter;
}

.ql-editor ol li:before {
  content: counter(list-counter) "." !important;
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  color: currentColor !important;
  font-weight: bold !important;
  min-width: 1.5rem !important;
}

/* Reset counter for nested lists */
.ql-editor ol ol,
.ql-editor ul ul {
  counter-reset: list-counter;
}

/* Ensure proper spacing and visibility */
.ql-editor li {
  min-height: 1.5em !important;
  line-height: 1.5 !important;
}

/* Social Icon Hover Effects */
.social-icon-hover:hover {
  background: var(--hover-bg) !important;
}

.social-icon-hover:hover .social-icon {
  color: var(--hover-text) !important;
  transform: scale(1.1);
}

/* TinyMCE Editor Styles */
.tinymce-editor-wrapper {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.tinymce-editor-wrapper .tox-tinymce {
  border: none !important;
}

.tinymce-editor-wrapper .tox-toolbar {
  border-bottom: 1px solid #e5e7eb !important;
  background: #f9fafb !important;
}

.tinymce-editor-wrapper .tox-edit-area {
  border: none !important;
}

/* Lexical Editor Styles */
.lexical-editor-wrapper {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
  background: white;
}

.editor-container {
  position: relative;
}

.toolbar {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  gap: 4px;
}

.toolbar-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  color: #374151;
  transition: all 0.2s;
}

.toolbar-item:hover {
  background: #e5e7eb;
}

.toolbar-item.active {
  background: #dbeafe;
  color: #2563eb;
}

.toolbar-item:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-item.spaced {
  margin-right: 4px;
}

.divider {
  width: 1px;
  height: 20px;
  background: #d1d5db;
  margin: 0 8px;
}

.editor-inner {
  position: relative;
  min-height: 200px;
}

.editor-input {
  min-height: 200px;
  padding: 16px;
  font-size: 16px;
  line-height: 1.6;
  color: #374151;
  outline: none;
  border: none;
  resize: none;
  font-family: inherit;
}

.editor-placeholder {
  position: absolute;
  top: 16px;
  left: 16px;
  color: #9ca3af;
  font-size: 16px;
  pointer-events: none;
  user-select: none;
}

.editor-paragraph {
  margin: 0 0 1rem 0;
}

.editor-quote {
  margin: 1.5rem 0;
  padding-left: 1rem;
  border-left: 4px solid #e5e7eb;
  font-style: italic;
  color: #6b7280;
}

.editor-heading-h1 {
  font-size: 1.875rem;
  font-weight: 700;
  margin: 1.5rem 0 0.75rem 0;
  line-height: 1.2;
}

.editor-heading-h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.25rem 0 0.5rem 0;
  line-height: 1.3;
}

.editor-heading-h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem 0;
  line-height: 1.4;
}

.editor-list-ol,
.editor-list-ul {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.editor-list-ul {
  list-style-type: disc;
}

.editor-list-ol {
  list-style-type: decimal;
}

.editor-listitem {
  margin-bottom: 0.25rem;
  padding-left: 0.25rem;
}

.editor-nested-listitem {
  list-style-type: circle;
}

.editor-link {
  color: #2563eb;
  text-decoration: underline;
  text-underline-offset: 2px;
}

.editor-link:hover {
  color: #1d4ed8;
}

.editor-text-bold {
  font-weight: 600;
}

.editor-text-italic {
  font-style: italic;
}

.editor-text-underline {
  text-decoration: underline;
}

.editor-text-strikethrough {
  text-decoration: line-through;
}

.editor-text-underlineStrikethrough {
  text-decoration: underline line-through;
}

.editor-text-code {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-family: 'Courier New', monospace;
}